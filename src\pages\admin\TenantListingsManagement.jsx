import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { 
  FiPlus, 
  FiEdit, 
  FiTrash2, 
  FiEye, 
  FiHome, 
  FiMapPin, 
  FiPhone,
  FiSearch,
  FiFilter,
  FiShoppingBag,
  FiStar
} from 'react-icons/fi';
import { supabase } from '../../utils/supabaseClient';
import { toast } from 'react-hot-toast';
import TenantForm from './TenantForm';
import { useAuthStore } from '../../store/useAuthStore';

const Container = styled.div`
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;

  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
`;

const Title = styled.h1`
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0;

  @media (max-width: 768px) {
    font-size: 2rem;
    text-align: center;
  }

  @media (max-width: 480px) {
    font-size: 1.75rem;
  }
`;

const CreateButton = styled(motion.button)`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #f16925, #e05a1a);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(241, 105, 37, 0.3);
  }

  @media (max-width: 768px) {
    width: 100%;
    justify-content: center;
    padding: 1rem;
  }
`;

const SearchBar = styled.div`
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0.5rem;
  }
`;

const SearchInput = styled.input`
  flex: 1;
  min-width: 250px;
  padding: 0.625rem 0.875rem;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #f16925;
  }

  @media (max-width: 768px) {
    min-width: auto;
    width: 100%;
    font-size: 16px; /* Prevents zoom on iOS */
  }
`;

const FilterSelect = styled.select`
  padding: 0.625rem 0.875rem;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #f16925;
  }

  @media (max-width: 768px) {
    width: 100%;
    font-size: 16px;
  }
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
`;

const StatCard = styled.div`
  background: white;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  border: 1px solid #e5e7eb;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  }

  @media (max-width: 768px) {
    padding: 0.75rem;
  }
`;

const StatIcon = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: #f16925;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
  margin: 0 auto 0.75rem auto;

  @media (max-width: 768px) {
    width: 28px;
    height: 28px;
    font-size: 0.875rem;
  }
`;

const StatValue = styled.div`
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.25rem;

  @media (max-width: 768px) {
    font-size: 1.25rem;
  }
`;

const StatLabel = styled.div`
  color: #6b7280;
  font-size: 0.75rem;
  font-weight: 500;

  @media (max-width: 768px) {
    font-size: 0.7rem;
  }
`;

// Desktop Table Styles
const TableContainer = styled.div`
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  border: 1px solid #e5e7eb;
  overflow: hidden;

  @media (max-width: 768px) {
    display: none; /* Hide table on mobile */
  }
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHeader = styled.thead`
  background: #f9fafb;
`;

const TableHeaderCell = styled.th`
  padding: 0.75rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
`;

const TableBody = styled.tbody``;

const TableRow = styled.tr`
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f9fafb;
  }
`;

const TableCell = styled.td`
  padding: 0.75rem;
  color: #374151;
  font-size: 0.875rem;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 0.5rem;
  align-items: center;
`;

const ActionButton = styled.button`
  padding: 0.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &.edit {
    background: #3b82f6;
    color: white;

    &:hover {
      background: #2563eb;
      transform: translateY(-1px);
    }
  }

  &.delete {
    background: #ef4444;
    color: white;

    &:hover {
      background: #dc2626;
      transform: translateY(-1px);
    }
  }

  &.view {
    background: #10b981;
    color: white;

    &:hover {
      background: #059669;
      transform: translateY(-1px);
    }
  }
`;

// Mobile Card Styles
const MobileContainer = styled.div`
  @media (min-width: 769px) {
    display: none; /* Hide on desktop */
  }
`;

const MobileCard = styled.div`
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  border: 1px solid #e5e7eb;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-1px);
  }
`;

const MobileCardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
`;

const MobileTenantName = styled.div`
  font-weight: 600;
  color: #1a1a1a;
  font-size: 1rem;
  line-height: 1.3;
  flex: 1;
  margin-right: 0.5rem;
`;

const MobileCategory = styled.span`
  padding: 0.2rem 0.5rem;
  border-radius: 16px;
  font-size: 0.7rem;
  font-weight: 500;
  white-space: nowrap;
  background: ${props => props.category === 'Retail' ? '#dbeafe' : '#fef3c7'};
  color: ${props => props.category === 'Retail' ? '#1e40af' : '#92400e'};
`;

const MobileCardContent = styled.div`
  margin-bottom: 0.5rem;
`;

const MobileInfoRow = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 0.375rem;
  font-size: 0.8rem;
  color: #666;
`;

const MobileInfoLabel = styled.span`
  font-weight: 500;
  color: #374151;
  min-width: 50px;
  margin-right: 0.5rem;
`;

const MobileInfoValue = styled.span`
  color: #666;
  word-break: break-word;
`;

const MobileCardFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 0.5rem;
  border-top: 1px solid #f0f0f0;
`;

const MobileDate = styled.span`
  font-size: 0.75rem;
  color: #666;
`;

const MobileActions = styled.div`
  display: flex;
  gap: 0.375rem;
`;

const MobileActionButton = styled.button`
  padding: 0.375rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;

  &.view {
    background: #10b981;
    color: white;
  }

  &.edit {
    background: #3b82f6;
    color: white;
  }

  &.delete {
    background: #ef4444;
    color: white;
  }

  &:hover {
    transform: translateY(-1px);
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem;
  color: #6b7280;

  @media (max-width: 768px) {
    padding: 2rem;
  }
`;

const EmptyIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #d1d5db;
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: #6b7280;

  @media (max-width: 768px) {
    padding: 1.5rem;
  }
`;

const TenantListingsManagement = () => {
  const [tenantListings, setTenantListings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [editingTenant, setEditingTenant] = useState(null);
  
  const { user } = useAuthStore();

  useEffect(() => {
    fetchTenantListings();
  }, []);

  const fetchTenantListings = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('tenant_listings')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setTenantListings(data || []);
    } catch {
      toast.error('Error fetching tenant listings');
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setEditingTenant(null);
    setShowForm(true);
  };

  const handleEdit = (tenant) => {
    setEditingTenant(tenant);
    setShowForm(true);
  };

  const handleDelete = async (id) => {
    if (!user) {
      toast.error('You must be logged in to delete tenants');
      return;
    }

    if (window.confirm('Are you sure you want to delete this tenant listing?')) {
      try {
        console.log('Attempting to delete tenant with ID:', id);
        console.log('Current user:', user);
        
        // First, let's check if the tenant exists and get its details
        const { data: existingTenant, error: fetchError } = await supabase
          .from('tenant_listings')
          .select('*')
          .eq('id', id)
          .single();

        if (fetchError) {
          console.error('Error fetching tenant before delete:', fetchError);
          toast.error(`Error fetching tenant: ${fetchError.message}`);
          return;
        }

        if (!existingTenant) {
          toast.error('Tenant not found');
          return;
        }

        console.log('Tenant found, proceeding with delete:', existingTenant);
        
        // Test if we can read from the table (RLS test)
        const { data: testRead, error: readError } = await supabase
          .from('tenant_listings')
          .select('count')
          .limit(1);
        
        console.log('RLS read test:', { testRead, readError });
        
        // Attempt to delete
        const { error: deleteError } = await supabase
          .from('tenant_listings')
          .delete()
          .eq('id', id);

        if (deleteError) {
          console.error('Supabase delete error:', deleteError);
          
          // Check if it's an RLS policy issue
          if (deleteError.message.includes('policy') || deleteError.message.includes('RLS')) {
            toast.error('Delete blocked by security policy. Please check your permissions.');
          } else {
            toast.error(`Delete failed: ${deleteError.message}`);
          }
          return;
        }
        
        console.log('Tenant deleted successfully');
        toast.success('Tenant listing deleted successfully');
        fetchTenantListings();
      } catch (error) {
        console.error('Error in handleDelete:', error);
        toast.error(`Error deleting tenant listing: ${error.message || 'Unknown error'}`);
      }
    }
  };

  const handleFormSubmit = async (formData, editingTenant) => {
    try {
      // Clean up the form data - remove empty strings and null values
      const cleanFormData = {};
      Object.entries(formData).forEach(([key, value]) => {
        if (value !== '' && value !== null && value !== undefined) {
          cleanFormData[key] = value;
        }
      });

      console.log('Submitting form data:', cleanFormData);

      if (editingTenant) {
        // Update existing tenant
        const { error } = await supabase
          .from('tenant_listings')
          .update(cleanFormData)
          .eq('id', editingTenant.id);

        if (error) {
          console.error('Update error:', error);
          throw error;
        }
        toast.success('Tenant listing updated successfully');
      } else {
        // Create new tenant
        const { error } = await supabase
          .from('tenant_listings')
          .insert([cleanFormData]);

        if (error) {
          console.error('Insert error:', error);
          throw error;
        }
        toast.success('Tenant listing created successfully');
      }
    } catch (error) {
      console.error('Form submission error:', error);
      toast.error(`Error ${editingTenant ? 'updating' : 'creating'} tenant listing: ${error.message || 'Unknown error'}`);
      throw error; // Re-throw to let the form component handle it
    }
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    fetchTenantListings();
  };

  const handleViewTenant = (tenant) => {
    if (!tenant) {
      toast.error('Tenant data is missing');
      return;
    }

    // Log tenant data for debugging
    console.log('Viewing tenant:', tenant);
    console.log('Tenant slug:', tenant.slug);
    console.log('Tenant ID:', tenant.id);

    // For now, show tenant details in a toast instead of navigating
    // since the individual tenant pages seem to have routing issues
    const tenantInfo = `
      Name: ${tenant.tenant_name || 'N/A'}
      Unit: ${tenant.unit || 'N/A'}
      Category: ${tenant.category || 'N/A'}
      Phone: ${tenant.phone_default || 'N/A'}
      Created: ${tenant.created_at ? new Date(tenant.created_at).toLocaleDateString() : 'N/A'}
    `;
    
    toast.success(tenantInfo, {
      duration: 5000,
      style: {
        whiteSpace: 'pre-line',
        maxWidth: '400px'
      }
    });

    // TODO: Fix tenant detail routing or implement modal view
    // Original navigation code (commented out due to routing issues):
    // if (tenant.slug && tenant.slug.trim() !== '') {
    //   window.open(`/tenant/${tenant.slug}`, '_blank');
    // } else if (tenant.id) {
    //   window.open(`/tenant/${tenant.id}`, '_blank');
    // } else {
    //   toast.error('Cannot view tenant: missing identifier (slug or ID)');
    // }
  };

  const filteredTenantListings = tenantListings.filter(tenant => {
    const matchesSearch = tenant.tenant_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tenant.unit?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tenant.category?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !categoryFilter || tenant.category === categoryFilter;
    
    return matchesSearch && matchesCategory;
  });

  const stats = {
    total: tenantListings.length,
    retail: tenantListings.filter(t => t.category === 'Retail').length,
    beauty: tenantListings.filter(t => t.category === 'Beauty').length,
    withUnits: tenantListings.filter(t => t.units && t.units.length > 0).length
  };

  if (loading) {
    return (
      <Container>
        <LoadingSpinner>Loading tenant listings...</LoadingSpinner>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <Title>Tenant Listings Management</Title>
        <CreateButton
          onClick={handleCreate}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <FiPlus size={20} />
          Add New Tenant
        </CreateButton>
      </Header>

      <StatsGrid>
        <StatCard>
          <StatIcon>
            <FiHome />
          </StatIcon>
          <StatValue>{stats.total}</StatValue>
          <StatLabel>Total Tenants</StatLabel>
        </StatCard>
        <StatCard>
          <StatIcon>
            <FiShoppingBag />
          </StatIcon>
          <StatValue>{stats.retail}</StatValue>
          <StatLabel>Retail</StatLabel>
        </StatCard>
        <StatCard>
          <StatIcon>
            <FiStar />
          </StatIcon>
          <StatValue>{stats.beauty}</StatValue>
          <StatLabel>Beauty</StatLabel>
        </StatCard>
        <StatCard>
          <StatIcon>
            <FiMapPin />
          </StatIcon>
          <StatValue>{stats.withUnits}</StatValue>
          <StatLabel>With Units</StatLabel>
        </StatCard>
      </StatsGrid>

      <SearchBar>
        <SearchInput
          type="text"
          placeholder="Search by tenant name, unit, or category..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        <FilterSelect
          value={categoryFilter}
          onChange={(e) => setCategoryFilter(e.target.value)}
        >
          <option value="">All Categories</option>
          <option value="Retail">Retail</option>
          <option value="Beauty">Beauty</option>
        </FilterSelect>
      </SearchBar>

      {/* Desktop Table View */}
      <TableContainer>
        {filteredTenantListings.length === 0 ? (
          <EmptyState>
            <EmptyIcon>🏪</EmptyIcon>
            <h3>No tenant listings found</h3>
            <p>Try adjusting your search or create a new tenant listing.</p>
          </EmptyState>
        ) : (
          <Table>
            <TableHeader>
              <tr>
                <TableHeaderCell>Tenant Name</TableHeaderCell>
                <TableHeaderCell>Unit</TableHeaderCell>
                <TableHeaderCell>Category</TableHeaderCell>
                <TableHeaderCell>Phone</TableHeaderCell>
                <TableHeaderCell>Created</TableHeaderCell>
                <TableHeaderCell>Actions</TableHeaderCell>
              </tr>
            </TableHeader>
            <TableBody>
              {filteredTenantListings.map((tenant) => (
                <TableRow key={tenant.id}>
                  <TableCell>
                    <div>
                      <div style={{ fontWeight: '600', color: '#1a1a1a' }}>
                        {tenant.tenant_name}
                      </div>
                      {tenant.slug && (
                        <div style={{ fontSize: '0.8rem', color: '#6b7280' }}>
                          /{tenant.slug}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div>{tenant.unit || '-'}</div>
                      {tenant.unit_type && (
                        <div style={{ fontSize: '0.8rem', color: '#6b7280' }}>
                          {tenant.unit_type}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <span style={{
                      padding: '0.25rem 0.75rem',
                      borderRadius: '20px',
                      fontSize: '0.8rem',
                      fontWeight: '500',
                      background: tenant.category === 'Retail' ? '#dbeafe' : '#fef3c7',
                      color: tenant.category === 'Retail' ? '#1e40af' : '#92400e'
                    }}>
                      {tenant.category || 'Uncategorized'}
                    </span>
                  </TableCell>
                  <TableCell>{tenant.phone_default || '-'}</TableCell>
                  <TableCell>
                    {new Date(tenant.created_at).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <ActionButtons>
                      <ActionButton
                        className="view"
                        onClick={() => handleViewTenant(tenant)}
                        title="View Tenant Details"
                      >
                        <FiEye size={16} />
                      </ActionButton>
                      <ActionButton
                        className="edit"
                        onClick={() => handleEdit(tenant)}
                        title="Edit Tenant"
                      >
                        <FiEdit size={16} />
                      </ActionButton>
                      <ActionButton
                        className="delete"
                        onClick={() => handleDelete(tenant.id)}
                        title="Delete Tenant"
                      >
                        <FiTrash2 size={16} />
                      </ActionButton>
                    </ActionButtons>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </TableContainer>

      {/* Mobile Card View */}
      <MobileContainer>
        {filteredTenantListings.length === 0 ? (
          <EmptyState>
            <EmptyIcon>🏪</EmptyIcon>
            <h3>No tenant listings found</h3>
            <p>Try adjusting your search or create a new tenant listing.</p>
          </EmptyState>
        ) : (
          filteredTenantListings.map((tenant) => (
            <MobileCard key={tenant.id}>
              <MobileCardHeader>
                <MobileTenantName>{tenant.tenant_name}</MobileTenantName>
                <MobileCategory category={tenant.category}>
                  {tenant.category || 'Uncategorized'}
                </MobileCategory>
              </MobileCardHeader>
              
              <MobileCardContent>
                <MobileInfoRow>
                  <MobileInfoLabel>Unit:</MobileInfoLabel>
                  <MobileInfoValue>{tenant.unit || '-'}</MobileInfoValue>
                </MobileInfoRow>
                
                {tenant.unit_type && (
                  <MobileInfoRow>
                    <MobileInfoLabel>Type:</MobileInfoLabel>
                    <MobileInfoValue>{tenant.unit_type}</MobileInfoValue>
                  </MobileInfoRow>
                )}
                
                <MobileInfoRow>
                  <MobileInfoLabel>Phone:</MobileInfoLabel>
                  <MobileInfoValue>{tenant.phone_default || '-'}</MobileInfoValue>
                </MobileInfoRow>
                
                {tenant.slug && (
                  <MobileInfoRow>
                    <MobileInfoLabel>Slug:</MobileInfoLabel>
                    <MobileInfoValue>/{tenant.slug}</MobileInfoValue>
                  </MobileInfoRow>
                )}
              </MobileCardContent>
              
              <MobileCardFooter>
                <MobileDate>
                  Created: {new Date(tenant.created_at).toLocaleDateString()}
                </MobileDate>
                
                <MobileActions>
                  <MobileActionButton
                    className="view"
                    onClick={() => handleViewTenant(tenant)}
                    title="View Tenant Details"
                  >
                    <FiEye size={16} />
                  </MobileActionButton>
                  <MobileActionButton
                    className="edit"
                    onClick={() => handleEdit(tenant)}
                    title="Edit Tenant"
                  >
                    <FiEdit size={16} />
                  </MobileActionButton>
                  <MobileActionButton
                    className="delete"
                    onClick={() => handleDelete(tenant.id)}
                    title="Delete Tenant"
                  >
                    <FiTrash2 size={16} />
                  </MobileActionButton>
                </MobileActions>
              </MobileCardFooter>
            </MobileCard>
          ))
        )}
      </MobileContainer>

      <TenantForm
        isOpen={showForm}
        onClose={() => setShowForm(false)}
        onSubmit={handleFormSubmit}
        editingTenant={editingTenant}
        onSuccess={handleFormSuccess}
      />
    </Container>
  );
};

export default TenantListingsManagement;