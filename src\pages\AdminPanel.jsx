import React from 'react';
import { useLocation, Navigate } from 'react-router-dom';
import AdminDashboardLayout from '../components/dashboard/AdminDashboardLayout';
import EventsManagement from './admin/EventsManagement';
import UserManagement from './admin/UserManagement';
import TenantListingsManagement from './admin/TenantListingsManagement';

const AdminPanel = () => {
  const location = useLocation();

  // Determine which component to render based on the current path
  const renderContent = () => {
    if (location.pathname === '/admin/events') {
      return <EventsManagement />;
    } else if (location.pathname === '/admin/users') {
      return <UserManagement />;
    } else if (location.pathname === '/admin/tenants') {
      return <TenantListingsManagement />;
    }
    // Redirect to events management by default
    return <Navigate to="/admin/events" replace />;
  };

  return (
    <AdminDashboardLayout>
      {renderContent()}
    </AdminDashboardLayout>
  );
};

export default AdminPanel;
