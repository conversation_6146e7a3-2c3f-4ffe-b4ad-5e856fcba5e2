import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { LogOut } from 'lucide-react';
import { useUserStore } from '../../store/useUserStore';
import { supabase } from '../../utils/supabaseClient';

const NavbarContainer = styled(motion.nav)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  @media (max-width: 768px) {
    padding: 0 1rem;
  }
`;

const LeftSection = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const RightSection = styled.div`
  display: flex;
  align-items: center;
  gap: 2rem;

  @media (max-width: 768px) {
    gap: 1rem;
  }
`;

const UserSection = styled.div`
  display: flex;
  align-items: center;
  gap: 1.5rem;

  @media (max-width: 768px) {
    gap: 1rem;
  }
`;

const UserInfo = styled.div`
  text-align: right;
  min-width: 120px;

  @media (max-width: 768px) {
    display: none;
  }
`;

const UserName = styled.div`
  font-weight: 600;
  color: #111827;
  font-size: 1rem;
  white-space: nowrap;
`;

const UserRole = styled.div`
  font-size: 0.85rem;
  color: #6b7280;
  white-space: nowrap;
`;

const LogoutButton = styled(motion.button)`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: #f16925;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex-shrink: 0;

  &:hover {
    background: #e05a1a;
  }

  @media (max-width: 768px) {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
`;

const AdminTopNavbar = () => {
  const { userData, signOut } = useUserStore();

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut();
      signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  // Get the actual user name from Supabase data
  const getUserDisplayName = () => {
    if (userData?.name && userData?.lastname) {
      // Remove "Admin" from the name if it exists
      const cleanName = userData.name.replace(/^Admin\s*/i, '');
      return `${cleanName} ${userData.lastname}`;
    } else if (userData?.name) {
      // Remove "Admin" from the name if it exists
      return userData.name.replace(/^Admin\s*/i, '');
    } else if (userData?.email) {
      return userData.email.split('@')[0]; // Use email prefix as fallback
    }
    return 'Usuario'; // Changed from 'Admin' to 'Usuario'
  };

  return (
    <NavbarContainer
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <LeftSection>
        {/* Menu toggle button removed - now handled by AdminMobileMenu */}
      </LeftSection>

      <RightSection>
        <UserSection>
          <UserInfo>
            <UserName>{getUserDisplayName()}</UserName>
            <UserRole>Administrador</UserRole>
          </UserInfo>
        </UserSection>

        <LogoutButton
          onClick={handleSignOut}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <LogOut size={16} />
          Cerrar Sesión
        </LogoutButton>
      </RightSection>
    </NavbarContainer>
  );
};

export default AdminTopNavbar;
