import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { <PERSON><PERSON><PERSON><PERSON>, FiS<PERSON>ch, FiCalendar, FiShield, FiFilter } from 'react-icons/fi';
import { supabase } from '../../utils/supabaseClient';

const Container = styled.div`
  padding: 2rem;
  width: 100%;
  margin: 0;
  box-sizing: border-box;
  overflow-x: hidden;

  @media (max-width: 1400px) {
    padding: 1.75rem;
    width: 100%;
  }

  @media (max-width: 1200px) {
    padding: 1.5rem;
    width: 100%;
  }

  @media (max-width: 768px) {
    padding: 1rem;
    width: 100%;
  }

  @media (max-width: 480px) {
    padding: 0.75rem;
    width: 100%;
  }
`;

const Header = styled.div`
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    margin-bottom: 1.5rem;
    text-align: center;
  }
`;

const Title = styled.h1`
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 0.5rem 0;

  @media (max-width: 768px) {
    font-size: 2rem;
  }

  @media (max-width: 480px) {
    font-size: 1.75rem;
  }
`;

const Subtitle = styled.p`
  font-size: 1.1rem;
  color: #666;
  margin: 0;

  @media (max-width: 768px) {
    font-size: 1rem;
  }
`;

const SearchBar = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  align-items: center;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
  }
`;

const SearchInput = styled.input`
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.2s ease;

  &:focus {
    border-color: #f16925;
  }

  @media (max-width: 768px) {
    width: 100%;
    font-size: 16px; /* Prevents zoom on iOS */
  }
`;

const FilterButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #f16925;
    color: #f16925;
  }

  @media (max-width: 768px) {
    width: 100%;
    justify-content: center;
  }
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;

  @media (max-width: 1400px) {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 0.7rem;
    margin-bottom: 0.9rem;
  }

  @media (max-width: 1200px) {
    grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
    gap: 0.6rem;
    margin-bottom: 0.875rem;
  }

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    margin-bottom: 0.75rem;
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
`;

const StatCard = styled.div`
  background: white;
  padding: 0.75rem;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  @media (max-width: 1400px) {
    padding: 0.7rem;
    gap: 0.45rem;
  }

  @media (max-width: 1200px) {
    padding: 0.6rem;
    gap: 0.4rem;
  }

  @media (max-width: 768px) {
    padding: 0.5rem;
    gap: 0.375rem;
  }

  @media (max-width: 480px) {
    padding: 0.5rem;
    gap: 0.375rem;
  }
`;

const StatHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;

  @media (max-width: 1400px) {
    gap: 0.45rem;
  }

  @media (max-width: 1200px) {
    gap: 0.4rem;
  }

  @media (max-width: 768px) {
    gap: 0.375rem;
  }
`;

const StatIcon = styled.div`
  width: 28px;
  height: 28px;
  border-radius: 4px;
  background: #f16925;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.875rem;
  flex-shrink: 0;

  @media (max-width: 1400px) {
    width: 27px;
    height: 27px;
    font-size: 0.85rem;
  }

  @media (max-width: 1200px) {
    width: 26px;
    height: 26px;
    font-size: 0.8rem;
  }

  @media (max-width: 768px) {
    width: 24px;
    height: 24px;
    font-size: 0.75rem;
  }

  @media (max-width: 480px) {
    width: 22px;
    height: 22px;
    font-size: 0.7rem;
  }
`;

const StatValue = styled.div`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0;

  @media (max-width: 1400px) {
    font-size: 1.05rem;
  }

  @media (max-width: 1200px) {
    font-size: 1rem;
  }

  @media (max-width: 768px) {
    font-size: 0.9rem;
  }

  @media (max-width: 480px) {
    font-size: 0.875rem;
  }
`;

const StatLabel = styled.div`
  color: #6b7280;
  font-size: 0.75rem;
  font-weight: 500;
  margin-left: auto;
  text-align: right;

  @media (max-width: 1400px) {
    font-size: 0.725rem;
  }

  @media (max-width: 1200px) {
    font-size: 0.7rem;
  }

  @media (max-width: 768px) {
    font-size: 0.65rem;
  }

  @media (max-width: 480px) {
    font-size: 0.6rem;
  }
`;

// Enhanced Desktop Table Styles
const UsersTable = styled.div`
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  width: 100%;
  min-width: 100%;
  box-sizing: border-box;

  @media (max-width: 1400px) {
    width: 100%;
    min-width: 100%;
  }

  @media (max-width: 1200px) {
    width: 100%;
    min-width: 100%;
  }

  @media (max-width: 768px) {
    border-radius: 6px;
    width: 100%;
    min-width: 100%;
  }

  @media (max-width: 480px) {
    width: 100%;
    min-width: 100%;
  }
`;

const TableHeader = styled.div`
  display: grid;
  grid-template-columns: minmax(180px, 1fr) minmax(280px, 1.5fr) minmax(100px, 0.8fr) minmax(140px, 1fr) minmax(80px, 0.6fr);
  padding: 0.75rem 1rem;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  width: 100%;

  > div {
    padding: 0.25rem 0;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  @media (max-width: 1400px) {
    grid-template-columns: minmax(160px, 1fr) minmax(250px, 1.4fr) minmax(90px, 0.7fr) minmax(130px, 0.9fr) minmax(80px, 0.5fr);
    padding: 0.75rem 0.875rem;
    font-size: 0.7rem;
  }

  @media (max-width: 1200px) {
    grid-template-columns: minmax(140px, 1fr) minmax(220px, 1.3fr) minmax(80px, 0.6fr) minmax(120px, 0.8fr) minmax(70px, 0.4fr);
    padding: 0.75rem 0.75rem;
    font-size: 0.7rem;
  }

  @media (max-width: 768px) {
    display: none; /* Hide header on mobile */
  }
`;

const TableRow = styled.div`
  display: grid;
  grid-template-columns: minmax(180px, 1fr) minmax(280px, 1.5fr) minmax(100px, 0.8fr) minmax(140px, 1fr) minmax(80px, 0.6fr);
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f3f4f6;
  align-items: center;
  transition: background-color 0.2s ease;
  width: 100%;

  &:hover {
    background-color: #f9fafb;
  }

  &:last-child {
    border-bottom: none;
  }

  @media (max-width: 1400px) {
    grid-template-columns: minmax(160px, 1fr) minmax(250px, 1.4fr) minmax(90px, 0.7fr) minmax(130px, 0.9fr) minmax(80px, 0.5fr);
    padding: 0.75rem 0.875rem;
  }

  @media (max-width: 1200px) {
    grid-template-columns: minmax(140px, 1fr) minmax(220px, 1.3fr) minmax(80px, 0.6fr) minmax(120px, 0.8fr) minmax(70px, 0.4fr);
    padding: 0.75rem 0.75rem;
  }

  @media (max-width: 768px) {
    display: block;
    padding: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
  }
`;

const UserCell = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  padding: 0.25rem 0;
  align-items: flex-start;
  justify-content: center;

  @media (max-width: 768px) {
    display: none;
  }
`;

const UserName = styled.div`
  font-weight: 600;
  color: #111827;
  font-size: 0.875rem;
  line-height: 1.2;
`;

const UserEmail = styled.div`
  color: #6b7280;
  font-size: 0.75rem;
  word-break: break-word;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  width: 100%;
`;

const UserRole = styled.span`
  padding: 0.25rem 0.5rem;
  border-radius: 8px;
  font-size: 0.65rem;
  font-weight: 600;
  text-align: center;
  display: inline-block;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  white-space: nowrap;
  
  &.admin {
    background: #fef3c7;
    color: #92400e;
  }
  
  &.local {
    background: #dbeafe;
    color: #1e40af;
  }

  &.general {
    background: #f3f4f6;
    color: #374151;
  }

  @media (max-width: 768px) {
    margin-bottom: 0.5rem;
  }
`;

const UserDate = styled.div`
  color: #6b7280;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
`;

const UserStatus = styled.div`
  display: flex;
  align-items: center;
  gap: 0.375rem;
  color: #059669;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
`;

const StatusDot = styled.span`
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background: #10b981;
  display: inline-block;
`;

// Mobile Card Styles
const MobileUserCard = styled.div`
  display: none;

  @media (max-width: 768px) {
    display: block;
    padding: 0.75rem;
    border-top: 1px solid #e5e7eb;
  }
`;

const UserInfo = styled.div`
  @media (max-width: 768px) {
    margin-bottom: 0.75rem;
  }
`;

const UserMeta = styled.div`
  @media (max-width: 768px) {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #666;
  }
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  color: #666;

  @media (max-width: 768px) {
    padding: 2rem;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem;
  color: #6b7280;

  @media (max-width: 768px) {
    padding: 2rem;
  }
`;

const EmptyIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #d1d5db;
`;

const TableActions = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;

  @media (max-width: 768px) {
    display: none;
  }
`;

const ResultsCount = styled.div`
  color: #6b7280;
  font-size: 0.75rem;
`;

const SortOptions = styled.div`
  display: flex;
  gap: 0.375rem;
  align-items: center;
`;

const SortButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.3rem 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 3px;
  background: white;
  color: #6b7280;
  font-size: 0.7rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #f16925;
    color: #f16925;
  }

  &.active {
    background: #f16925;
    color: white;
    border-color: #f16925;
  }
`;

const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const [stats, setStats] = useState({
    totalUsers: 0,
    adminUsers: 0,
    localUsers: 0,
    activeUsers: 0
  });

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      
      // Fetch all users from public.users table
      const { data: usersData, error } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      setUsers(usersData || []);
      
      // Calculate stats
      const totalUsers = usersData?.length || 0;
      const adminUsers = usersData?.filter(u => u.role === 'admin' && u.is_admin)?.length || 0;
      const localUsers = usersData?.filter(u => u.role === 'local')?.length || 0;
      const activeUsers = usersData?.filter(u => u.created_at)?.length || 0;

      setStats({
        totalUsers,
        adminUsers,
        localUsers,
        activeUsers
      });

    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredUsers = users.filter(user => {
    const searchLower = searchTerm.toLowerCase();
    return (
      user.name?.toLowerCase().includes(searchLower) ||
      user.lastname?.toLowerCase().includes(searchLower) ||
      user.email?.toLowerCase().includes(searchLower) ||
      user.role?.toLowerCase().includes(searchLower)
    );
  });

  const sortedUsers = [...filteredUsers].sort((a, b) => {
    let aValue = a[sortBy];
    let bValue = b[sortBy];
    
    if (sortBy === 'name') {
      aValue = `${a.name} ${a.lastname}`;
      bValue = `${b.name} ${b.lastname}`;
    }
    
    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  });

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  if (loading) {
    return (
      <Container>
        <LoadingSpinner>Cargando usuarios....</LoadingSpinner>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <Title>Gestión de Usuarios</Title>
        <Subtitle>Administra todos los usuarios de la plataforma</Subtitle>
      </Header>

      <StatsGrid>
        <StatCard>
          <StatHeader>
            <StatIcon>
              <FiUsers />
            </StatIcon>
          </StatHeader>
          <StatValue>{stats.totalUsers}</StatValue>
          <StatLabel>Total de Usuarios</StatLabel>
        </StatCard>

        <StatCard>
          <StatHeader>
            <StatIcon>
              <FiShield />
            </StatIcon>
          </StatHeader>
          <StatValue>{stats.adminUsers}</StatValue>
          <StatLabel>Usuarios Manager</StatLabel>
        </StatCard>

        <StatCard>
          <StatHeader>
            <StatIcon>
              <FiUsers />
            </StatIcon>
          </StatHeader>
          <StatValue>{stats.localUsers}</StatValue>
          <StatLabel>Usuarios Locales</StatLabel>
        </StatCard>

        <StatCard>
          <StatHeader>
            <StatIcon>
              <FiCalendar />
            </StatIcon>
          </StatHeader>
          <StatValue>{stats.activeUsers}</StatValue>
          <StatLabel>Usuarios Activos</StatLabel>
        </StatCard>
      </StatsGrid>

      <SearchBar>
        <FiSearch size={20} color="#666" />
        <SearchInput
          type="text"
          placeholder="Buscar usuarios por nombre, email o rol..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        <FilterButton>
          <FiFilter size={16} />
          Filtros
        </FilterButton>
      </SearchBar>

      <UsersTable>
        <TableActions>
          <ResultsCount>
            {sortedUsers.length} usuario{sortedUsers.length !== 1 ? 's' : ''} encontrado{sortedUsers.length !== 1 ? 's' : ''}
          </ResultsCount>
          <SortOptions>
            <SortButton 
              className={sortBy === 'name' ? 'active' : ''}
              onClick={() => handleSort('name')}
            >
              Nombre {sortBy === 'name' && (sortOrder === 'asc' ? '↑' : '↓')}
            </SortButton>
            <SortButton 
              className={sortBy === 'created_at' ? 'active' : ''}
              onClick={() => handleSort('created_at')}
            >
              Fecha {sortBy === 'created_at' && (sortOrder === 'asc' ? '↑' : '↓')}
            </SortButton>
            <SortButton 
              className={sortBy === 'role' ? 'active' : ''}
              onClick={() => handleSort('role')}
            >
              Rol {sortBy === 'role' && (sortOrder === 'asc' ? '↑' : '↓')}
            </SortButton>
          </SortOptions>
        </TableActions>

        <TableHeader>
          <div>Usuario</div>
          <div>Email</div>
          <div>Rol</div>
          <div>Fecha de Registro</div>
          <div>Estado</div>
        </TableHeader>

        {sortedUsers.length === 0 ? (
          <EmptyState>
            <EmptyIcon>👥</EmptyIcon>
            <h3>No se encontraron usuarios</h3>
            <p>Intenta ajustar tu búsqueda o crear un nuevo usuario.</p>
          </EmptyState>
        ) : (
          sortedUsers.map((user) => (
            <TableRow key={user.id}>
              <UserCell>
                <UserName>{user.name} {user.lastname}</UserName>
              </UserCell>
              <UserCell>
                <UserEmail>{user.email}</UserEmail>
              </UserCell>
              <UserCell>
                <UserRole className={user.role}>
                  {user.role === 'admin' && user.is_admin ? 
                    (user.email === '<EMAIL>' ? 'Admin' : 'Manager') : 
                    user.role}
                </UserRole>
              </UserCell>
              <UserCell>
                <UserDate>{formatDate(user.created_at)}</UserDate>
              </UserCell>
              <UserCell>
                <UserStatus>
                  <StatusDot />
                  Activo
                </UserStatus>
              </UserCell>

              {/* Mobile View */}
              <MobileUserCard>
                <UserInfo>
                  <UserName>{user.name} {user.lastname}</UserName>
                  <UserEmail>{user.email}</UserEmail>
                  <UserRole className={user.role}>
                    {user.role === 'admin' && user.is_admin ? 
                      (user.email === '<EMAIL>' ? 'Admin' : 'Manager') : 
                      user.role}
                  </UserRole>
                </UserInfo>
                <UserMeta>
                  <span>{formatDate(user.created_at)}</span>
                  <span style={{ color: '#10b981' }}>● Activo</span>
                </UserMeta>
              </MobileUserCard>
            </TableRow>
          ))
        )}
      </UsersTable>
    </Container>
  );
};

export default UserManagement;
