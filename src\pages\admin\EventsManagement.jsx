import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Calendar, 
  MapPin, 
  User, 
  Star,
  Search,
  Filter,
  Upload,
  X,
  Check,
  Clock,
  AlertCircle
} from 'lucide-react';
import { supabase } from '../../utils/supabaseClient';
import { toast } from 'react-hot-toast';

const Container = styled.div`
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;

  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
`;

const Title = styled.h1`
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0;

  @media (max-width: 768px) {
    font-size: 2rem;
    text-align: center;
  }
`;

const CreateButton = styled(motion.button)`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #f16925, #e05a1a);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(241, 105, 37, 0.3);
  }
`;

const SearchBar = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0.75rem;
  }
`;

const SearchInput = styled.input`
  flex: 1;
  min-width: 300px;
  padding: 0.75rem 1rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #667eea;
  }

  @media (max-width: 768px) {
    min-width: auto;
  }
`;

const FilterSelect = styled.select`
  padding: 0.75rem 1rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #667eea;
  }
`;

const EventsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
`;

const EventCard = styled(motion.div)`
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e1e5e9;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const EventImage = styled.div`
  width: 100%;
  height: 200px;
  border-radius: 8px;
  background: ${props => props.$imageUrl ? `url(${props.$imageUrl})` : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'};
  background-size: cover;
  background-position: center;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  position: relative;
`;

const StatusBadge = styled.div`
  position: absolute;
  top: 0.75rem;
  left: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  background: ${props => {
    switch (props.$status) {
      case 'active': return '#10b981';
      case 'upcoming': return '#f59e0b';
      case 'expired': return '#ef4444';
      case 'cancelled': return '#dc2626';
      case 'postponed': return '#7c3aed';
      default: return '#6b7280';
    }
  }};
`;
//#tailwind ist better

const FeaturedBadge = styled.div`
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: #ffd700;
  color: #1a1a1a;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
`;

const EventTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
`;

const EventInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #666;
  font-size: 0.9rem;
`;

const EventDescription = styled.p`
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0.5rem 0 1rem 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const EventActions = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0.5rem;
  }
`;

const ActionButton = styled.button`
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;

  @media (max-width: 768px) {
    width: 100%;
    padding: 0.75rem 1rem;
  }
`;

const ViewButton = styled(ActionButton)`
  background: #e3f2fd;
  color: #1976d2;

  &:hover {
    background: #bbdefb;
  }
`;

const EditButton = styled(ActionButton)`
  background: #fff3e0;
  color: #f57c00;

  &:hover {
    background: #ffe0b2;
  }
`;

const DeleteButton = styled(ActionButton)`
  background: #ffebee;
  color: #d32f2f;

  &:hover {
    background: #ffcdd2;
  }
`;

const Modal = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled(motion.div)`
  background: white;
  border-radius: 16px;
  padding: 2rem;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;

  @media (max-width: 768px) {
    width: 95%;
    padding: 1.5rem;
    max-height: 95vh;
  }
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
`;

const ModalTitle = styled.h2`
  font-size: 1.8rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f3f4f6;
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
`;

const Input = styled.input`
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.2s ease;

  &:focus {
    border-color: #f16925;
  }
`;

const Textarea = styled.textarea`
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.2s ease;
  resize: vertical;
  min-height: 100px;

  &:focus {
    border-color: #f16925;
  }
`;

const Select = styled.select`
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  outline: none;
  transition: border-color 0.2s ease;

  &:focus {
    border-color: #f16925;
  }
`;

const CheckboxGroup = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const Checkbox = styled.input`
  width: 1.2rem;
  height: 1.2rem;
  cursor: pointer;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
`;

const Button = styled.button`
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;

  &.primary {
    background: #f16925;
    color: white;

    &:hover {
      background: #e05a1a;
    }
  }

  &.secondary {
    background: #6b7280;
    color: white;

    &:hover {
      background: #4b5563;
    }
  }
`;

const ImageUpload = styled.div`
  border: 2px dashed #e1e5e9;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #667eea;
    background: #f8f9ff;
  }
`;

const ImagePreview = styled.div`
  position: relative;
  display: inline-block;
  margin-top: 1rem;
`;

const RemoveImage = styled.button`
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  background: #ff4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 1.5rem;
  height: 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem;
  color: #666;
`;

const EmptyIcon = styled.div`
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
`;

const EventsManagement = () => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showModal, setShowModal] = useState(false);
  const [editingEvent, setEditingEvent] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    location: '',
    start_date: '',
    end_date: '',
    start_time: '',
    end_time: '',
    organizer: '',
    link: '',
    is_featured: false,
    status: 'active',
    image_url: '',
    banner_url: '',
    // Localized fields for future English translations
    title_es: '',
    title_en: '',
    description_es: '',
    description_en: '',
    category_es: '',
    category_en: '',
    location_es: '',
    location_en: '',
    organizer_es: '',
    organizer_en: ''
  });
  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newEvent, setNewEvent] = useState({
    title: '',
    description: '',
    start_date: '',
    end_date: '',
    status: 'active',
    location: '',
    image_url: ''
  });

  useEffect(() => {
    fetchEvents();
  }, []);

  const fetchEvents = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('events')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setEvents(data || []);
    } catch (error) {
      toast.error('Error fetching events');
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateEvent = () => {
    setEditingEvent(null);
    setFormData({
      title: '',
      description: '',
      category: '',
      location: '',
      start_date: '',
      end_date: '',
      start_time: '',
      end_time: '',
      organizer: '',
      link: '',
      is_featured: false,
      status: 'active',
      image_url: '',
      banner_url: '',
      // Localized fields for future English translations
      title_es: '',
      title_en: '',
      description_es: '',
      description_en: '',
      category_es: '',
      category_en: '',
      location_es: '',
      location_en: '',
      organizer_es: '',
      organizer_en: ''
    });
    setImageFile(null);
    setImagePreview('');
    setShowModal(true);
  };

  const handleEditEvent = (event) => {
    setEditingEvent(event);
    
    setFormData({
      title: event.title || '',
      description: event.description || '',
      category: event.category || '',
      location: event.location || '',
      start_date: event.start_date || '',
      end_date: event.end_date || '',
      start_time: event.start_time || '',
      end_time: event.end_time || '',
      organizer: event.organizer || '',
      link: event.link || '',
      is_featured: event.is_featured || false,
      status: event.status || 'active',
      image_url: event.image_url || '',
      banner_url: event.banner_url || '',
      // Localized fields - populate with existing values or fallback to main fields
      title_es: event.title_es || event.title || '',
      title_en: event.title_en || '',
      description_es: event.description_es || event.description || '',
      description_en: event.description_en || '',
      category_es: event.category_es || event.category || '',
      category_en: event.category_en || '',
      location_es: event.location_es || event.location || '',
      location_en: event.location_en || '',
      organizer_es: event.organizer_es || event.organizer || '',
      organizer_en: event.organizer_en || ''
    });
    setImagePreview(event.image_url || '');
    setShowModal(true);
  };

  const handleDeleteEvent = async (eventId) => {
    if (window.confirm('¿Estás seguro de que quieres eliminar este evento?')) {
      try {
        const { error } = await supabase
          .from('events')
          .delete()
          .eq('id', eventId);

        if (error) throw error;

        toast.success('Evento eliminado exitosamente');
        fetchEvents();
      } catch (error) {
        toast.error('Error eliminando evento');
        console.error('Error:', error);
      }
    }
  };



  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setImageFile(file);
      const reader = new FileReader();
      reader.onload = (e) => setImagePreview(e.target.result);
      reader.readAsDataURL(file);
    }
  };

  const uploadImage = async (file) => {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${Math.random()}.${fileExt}`;
      const filePath = `events/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('images')
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from('images')
        .getPublicUrl(filePath);

      return publicUrl;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      let avatarUrl = formData.image_url;
      let bannerUrl = formData.banner_url;
      
      if (imageFile) {
        avatarUrl = await uploadImage(imageFile);
      }

      // Create event data matching the actual events table structure
      const eventData = {
        title: formData.title_es || formData.title_en || 'Sin título',
        description: formData.description_es || formData.description_en || '',
        category: formData.category_es || formData.category_en || '',
        location: formData.location_es || formData.location_en || '',
        start_date: formData.start_date,
        end_date: formData.end_date || null,
        start_time: formData.start_time || null,
        end_time: formData.end_time || null,
        organizer: formData.organizer_es || formData.organizer_en || '',
        link: formData.link || null,
        image_url: avatarUrl || null,
        banner_url: bannerUrl || null,
        status: formData.status,
        is_featured: formData.is_featured,
        // Localized fields
        title_es: formData.title_es || null,
        title_en: formData.title_en || null,
        description_es: formData.description_es || null,
        description_en: formData.description_en || null,
        category_es: formData.category_es || null,
        category_en: formData.category_en || null,
        location_es: formData.location_es || null,
        location_en: formData.location_en || null,
        organizer_es: formData.organizer_es || null,
        organizer_en: formData.organizer_en || null
      };

      if (editingEvent) {
        // For updates, we need to handle status changes carefully
        // If status is being changed manually, we want to respect that choice
        const { error } = await supabase
          .from('events')
          .update(eventData)
          .eq('id', editingEvent.id);

        if (error) throw error;
        toast.success('Evento actualizado exitosamente');
      } else {
        const { error } = await supabase
          .from('events')
          .insert([eventData]);

        if (error) throw error;
        toast.success('Evento creado exitosamente');
      }

      setShowModal(false);
      fetchEvents();
    } catch (error) {
      toast.error(`Error guardando evento: ${error.message}`);
      console.error('Error:', error);
    }
  };

  const handleCreateEventForm = async (e) => {
    e.preventDefault();
    
    try {
      const { data, error } = await supabase
        .from('events')
        .insert([newEvent])
        .select();

      if (error) throw error;

      // Add new event to the list
      setEvents(prev => [data[0], ...prev]);
      
      // Reset form and close modal
      setNewEvent({
        title: '',
        description: '',
        start_date: '',
        end_date: '',
        status: 'active',
        location: '',
        image_url: ''
      });
      setShowCreateModal(false);
      
      // Refresh events list
      fetchEvents();
      
    } catch (error) {
      console.error('Error creating event:', error);
      alert('Error al crear el evento');
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewEvent(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const filteredEvents = events.filter(event => {
    const matchesSearch = event.title_es?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.title_en?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.description_es?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.description_en?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.category_es?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.category_en?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.category?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || event.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active': return <Check size={16} />;
      case 'upcoming': return <Clock size={16} />;
      case 'expired': return <AlertCircle size={16} />;
      case 'cancelled': return <X size={16} />;
      case 'postponed': return <Clock size={16} />;
      default: return <AlertCircle size={16} />;
    }
  };

  if (loading) {
    return (
      <Container>
        <div style={{ textAlign: 'center', padding: '2rem' }}>
          <div style={{ fontSize: '1.5rem', color: '#666' }}>Cargando eventos...</div>
        </div>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <Title>Gestión de Eventos</Title>
        <CreateButton onClick={handleCreateEvent}>
          <Plus size={20} />
          Crear Evento
        </CreateButton>
      </Header>

      <SearchBar>
        <SearchInput
          type="text"
          placeholder="Buscar eventos..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        <FilterSelect
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
        >
          <option value="all">Todos los estados</option>
          <option value="active">Activo</option>
          <option value="upcoming">Próximo</option>
          <option value="expired">Expirado</option>
          <option value="cancelled">Cancelado</option>
          <option value="postponed">Aplazado</option>
        </FilterSelect>
      </SearchBar>

      {filteredEvents.length === 0 ? (
        <EmptyState>
          <EmptyIcon>📅</EmptyIcon>
          <h3>No se encontraron eventos</h3>
          <p>Comienza creando tu primer evento o ajusta los filtros de búsqueda.</p>
        </EmptyState>
      ) : (
        <EventsGrid>
          {filteredEvents.map((event) => (
            <EventCard
              key={event.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <EventImage $imageUrl={event.image_url}>
                {!event.image_url && 'Sin imagen'}
                <StatusBadge $status={event.status}>
                  {getStatusIcon(event.status)}
                  {event.status === 'active' ? 'Activo' : 
                   event.status === 'upcoming' ? 'Próximo' : 
                   event.status === 'expired' ? 'Expirado' :
                   event.status === 'cancelled' ? 'Cancelado' :
                   event.status === 'postponed' ? 'Aplazado' : 'Desconocido'}
                </StatusBadge>
                {event.is_featured && <FeaturedBadge>Destacado</FeaturedBadge>}
              </EventImage>
              
              <EventTitle>{event.title_es || event.title_en || event.title || 'Sin título'}</EventTitle>
              
              <EventInfo>
                <Calendar size={16} />
                {event.start_date ? new Date(event.start_date).toLocaleDateString('es-ES') : 'Sin fecha'}
              </EventInfo>
              
              <EventInfo>
                <MapPin size={16} />
                {event.location_es || event.location_en || event.location || 'Sin ubicación'}
              </EventInfo>
              
              <EventInfo>
                <User size={16} />
                {event.organizer_es || event.organizer_en || event.organizer || 'Sin organizador'}
              </EventInfo>
              
              <EventDescription>
                {event.description_es || event.description_en || event.description || 'Sin descripción'}
              </EventDescription>
              
              <EventActions>
                <ViewButton onClick={() => window.open(`/eventinfo/${event.id}`, '_blank')}>
                  <Eye size={16} />
                  Ver
                </ViewButton>
                <EditButton onClick={() => handleEditEvent(event)}>
                  <Edit size={16} />
                  Editar
                </EditButton>
                <DeleteButton onClick={() => handleDeleteEvent(event.id)}>
                  <Trash2 size={16} />
                  Eliminar
                </DeleteButton>
              </EventActions>
            </EventCard>
          ))}
        </EventsGrid>
      )}

      {showModal && (
        <Modal onClick={() => setShowModal(false)}>
          <ModalContent onClick={(e) => e.stopPropagation()}>
            <ModalHeader>
              <ModalTitle>
                {editingEvent ? 'Editar Evento' : 'Crear Nuevo Evento'}
              </ModalTitle>
              <CloseButton onClick={() => setShowModal(false)}>
                <X />
              </CloseButton>
            </ModalHeader>

            <Form onSubmit={handleSubmit}>
              <FormGroup>
                <Label>Título (Español) *</Label>
                <Input
                  type="text"
                  value={formData.title_es}
                  onChange={(e) => setFormData({...formData, title_es: e.target.value})}
                  required
                  placeholder="Título del evento en español"
                />
              </FormGroup>

              <FormGroup>
                <Label>Título (Inglés)</Label>
                <Input
                  type="text"
                  value={formData.title_en}
                  onChange={(e) => setFormData({...formData, title_en: e.target.value})}
                  placeholder="Title in English (optional)"
                />
              </FormGroup>

              <FormGroup>
                <Label>Descripción (Español)</Label>
                <Textarea
                  value={formData.description_es}
                  onChange={(e) => setFormData({...formData, description_es: e.target.value})}
                  placeholder="Descripción del evento en español"
                />
              </FormGroup>

              <FormGroup>
                <Label>Descripción (Inglés)</Label>
                <Textarea
                  value={formData.description_en}
                  onChange={(e) => setFormData({...formData, description_en: e.target.value})}
                  placeholder="Description in English (optional)"
                />
              </FormGroup>

              <FormGroup>
                <Label>Categoría (Español)</Label>
                <Input
                  type="text"
                  value={formData.category_es}
                  onChange={(e) => setFormData({...formData, category_es: e.target.value})}
                  placeholder="Categoría en español"
                />
              </FormGroup>

              <FormGroup>
                <Label>Categoría (Inglés)</Label>
                <Input
                  type="text"
                  value={formData.category_en}
                  onChange={(e) => setFormData({...formData, category_en: e.target.value})}
                  placeholder="Category in English (optional)"
                />
              </FormGroup>

              <FormGroup>
                <Label>Ubicación (Español)</Label>
                <Input
                  type="text"
                  value={formData.location_es}
                  onChange={(e) => setFormData({...formData, location_es: e.target.value})}
                  placeholder="Ubicación en español"
                />
              </FormGroup>

              <FormGroup>
                <Label>Ubicación (Inglés)</Label>
                <Input
                  type="text"
                  value={formData.location_en}
                  onChange={(e) => setFormData({...formData, location_en: e.target.value})}
                  placeholder="Location in English (optional)"
                />
              </FormGroup>

              <FormGroup>
                <Label>Organizador (Español)</Label>
                <Input
                  type="text"
                  value={formData.organizer_es}
                  onChange={(e) => setFormData({...formData, organizer_es: e.target.value})}
                  placeholder="Organizador en español"
                />
              </FormGroup>

              <FormGroup>
                <Label>Organizador (Inglés)</Label>
                <Input
                  type="text"
                  value={formData.organizer_en}
                  onChange={(e) => setFormData({...formData, organizer_en: e.target.value})}
                  placeholder="Organizer in English (optional)"
                />
              </FormGroup>

              <FormGroup>
                <Label>Fecha de inicio *</Label>
                <Input
                  type="date"
                  value={formData.start_date}
                  onChange={(e) => setFormData({...formData, start_date: e.target.value})}
                  required
                />
              </FormGroup>

              <FormGroup>
                <Label>Fecha de fin</Label>
                <Input
                  type="date"
                  value={formData.end_date}
                  onChange={(e) => setFormData({...formData, end_date: e.target.value})}
                />
              </FormGroup>

              <FormGroup>
                <Label>Hora de inicio</Label>
                <Input
                  type="time"
                  value={formData.start_time}
                  onChange={(e) => setFormData({...formData, start_time: e.target.value})}
                />
              </FormGroup>

              <FormGroup>
                <Label>Hora de fin</Label>
                <Input
                  type="time"
                  value={formData.end_time}
                  onChange={(e) => setFormData({...formData, end_time: e.target.value})}
                />
              </FormGroup>

              <FormGroup>
                <Label>Enlace</Label>
                <Input
                  type="url"
                  value={formData.link}
                  onChange={(e) => setFormData({...formData, link: e.target.value})}
                  placeholder="https://..."
                />
              </FormGroup>

              <FormGroup>
                <Label>Estado</Label>
                <Select
                  value={formData.status}
                  onChange={(e) => setFormData({...formData, status: e.target.value})}
                >
                  <option value="active">Activo</option>
                  <option value="upcoming">Próximo</option>
                  <option value="expired">Expirado</option>
                  <option value="cancelled">Cancelado</option>
                  <option value="postponed">Aplazado</option>
                </Select>
                <small style={{ color: '#666', fontSize: '0.8rem', marginTop: '0.25rem' }}>
                  El estado se puede cambiar manualmente. Los eventos pasados se marcarán automáticamente como expirados.
                </small>
              </FormGroup>

              <FormGroup>
                <Label>Imagen del evento (Avatar)</Label>
                <ImageUpload>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    style={{ display: 'none' }}
                    id="avatar-upload"
                  />
                  <label htmlFor="avatar-upload" style={{ cursor: 'pointer' }}>
                    <Upload size={24} />
                    <div style={{ marginTop: '0.5rem' }}>
                      {imagePreview ? 'Cambiar avatar' : 'Subir avatar del evento'}
                    </div>
                  </label>
                </ImageUpload>
                
                {imagePreview && (
                  <ImagePreview>
                    <img 
                      src={imagePreview} 
                      alt="Avatar Preview" 
                      style={{ 
                        width: '100px', 
                        height: '100px', 
                        objectFit: 'cover', 
                        borderRadius: '8px' 
                      }} 
                    />
                    <RemoveImage onClick={() => {
                      setImagePreview('');
                      setImageFile(null);
                    }}>
                      <X size={12} />
                    </RemoveImage>
                  </ImagePreview>
                )}
              </FormGroup>

              <FormGroup>
                <Label>Banner del evento</Label>
                <ImageUpload>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files[0];
                      if (file) {
                        const reader = new FileReader();
                        reader.onload = (e) => setFormData({...formData, banner_url: e.target.result});
                        reader.readAsDataURL(file);
                      }
                    }}
                    style={{ display: 'none' }}
                    id="banner-upload"
                  />
                  <label htmlFor="banner-upload" style={{ cursor: 'pointer' }}>
                    <Upload size={24} />
                    <div style={{ marginTop: '0.5rem' }}>
                      {formData.banner_url ? 'Cambiar banner' : 'Subir banner del evento'}
                    </div>
                  </label>
                </ImageUpload>
                
                {formData.banner_url && (
                  <ImagePreview>
                    <img 
                      src={formData.banner_url} 
                      alt="Banner Preview" 
                      style={{ 
                        width: '200px', 
                        height: '100px', 
                        objectFit: 'cover', 
                        borderRadius: '8px' 
                      }} 
                    />
                    <RemoveImage onClick={() => setFormData({...formData, banner_url: ''})}>
                      <X size={12} />
                    </RemoveImage>
                  </ImagePreview>
                )}
              </FormGroup>

              <CheckboxGroup>
                <Checkbox
                  type="checkbox"
                  id="is_featured"
                  checked={formData.is_featured}
                  onChange={(e) => setFormData({...formData, is_featured: e.target.checked})}
                />
                <Label htmlFor="is_featured">Evento destacado</Label>
              </CheckboxGroup>

              <ButtonGroup>
                <Button type="button" className="secondary" onClick={() => setShowModal(false)}>
                  Cancelar
                </Button>
                <Button type="submit" className="primary">
                  {editingEvent ? 'Actualizar Evento' : 'Crear Evento'}
                </Button>
              </ButtonGroup>
            </Form>
          </ModalContent>
        </Modal>
      )}

      {/* Create Event Modal */}
      {showCreateModal && (
        <Modal
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <ModalContent
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
          >
            <ModalHeader>
              <ModalTitle>Crear Nuevo Evento</ModalTitle>
              <CloseButton onClick={() => setShowCreateModal(false)}>
                ×
              </CloseButton>
            </ModalHeader>

            <Form onSubmit={handleCreateEventForm}>
              <FormGroup>
                <Label>Título del Evento</Label>
                <Input
                  type="text"
                  name="title"
                  value={newEvent.title}
                  onChange={handleInputChange}
                  required
                  placeholder="Ingresa el título del evento"
                />
              </FormGroup>

              <FormGroup>
                <Label>Descripción</Label>
                <Textarea
                  name="description"
                  value={newEvent.description}
                  onChange={handleInputChange}
                  required
                  placeholder="Describe el evento"
                />
              </FormGroup>

              <FormGroup>
                <Label>Fecha de Inicio</Label>
                <Input
                  type="datetime-local"
                  name="start_date"
                  value={newEvent.start_date}
                  onChange={handleInputChange}
                  required
                />
              </FormGroup>

              <FormGroup>
                <Label>Fecha de Fin</Label>
                <Input
                  type="datetime-local"
                  name="end_date"
                  value={newEvent.end_date}
                  onChange={handleInputChange}
                  required
                />
              </FormGroup>

              <FormGroup>
                <Label>Estado</Label>
                <Select
                  name="status"
                  value={newEvent.status}
                  onChange={handleInputChange}
                  required
                >
                  <option value="active">Activo</option>
                  <option value="upcoming">Próximo</option>
                  <option value="expired">Expirado</option>
                  <option value="cancelled">Cancelado</option>
                  <option value="postponed">Aplazado</option>
                </Select>
              </FormGroup>

              <FormGroup>
                <Label>Ubicación</Label>
                <Input
                  type="text"
                  name="location"
                  value={newEvent.location}
                  onChange={handleInputChange}
                  placeholder="Ubicación del evento"
                />
              </FormGroup>

              <FormGroup>
                <Label>URL de Imagen</Label>
                <Input
                  type="url"
                  name="image_url"
                  value={newEvent.image_url}
                  onChange={handleInputChange}
                  placeholder="https://ejemplo.com/imagen.jpg"
                />
              </FormGroup>

              <ButtonGroup>
                <Button
                  type="button"
                  className="secondary"
                  onClick={() => setShowCreateModal(false)}
                >
                  Cancelar
                </Button>
                <Button type="submit" className="primary">
                  Crear Evento
                </Button>
              </ButtonGroup>
            </Form>
          </ModalContent>
        </Modal>
      )}
    </Container>
  );
};

export default EventsManagement;
