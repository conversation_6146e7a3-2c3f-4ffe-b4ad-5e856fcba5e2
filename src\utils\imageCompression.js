/**
 * Image compression utility for reducing file sizes before upload
 * Targets: Reduce 18-20MB images to under 500KB while maintaining quality
 */

/**
 * Compress an image file to reduce its size
 * @param {File} file - The image file to compress
 * @param {Object} options - Compression options
 * @returns {Promise<File>} - Compressed image file
 */
export const compressImage = async (file, options = {}) => {
  const {
    maxWidth = 1200,
    maxHeight = 800,
    quality = 0.8,
    maxSizeKB = 500,
    format = 'image/jpeg'
  } = options;

  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      try {
        // Calculate new dimensions while maintaining aspect ratio
        let { width, height } = calculateDimensions(img.width, img.height, maxWidth, maxHeight);

        canvas.width = width;
        canvas.height = height;

        // Draw and compress the image
        ctx.drawImage(img, 0, 0, width, height);

        // Try different quality levels to meet size target
        compressToTarget(canvas, format, quality, maxSizeKB, file.name)
          .then(resolve)
          .catch(reject);
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
};

/**
 * Calculate optimal dimensions while maintaining aspect ratio
 */
const calculateDimensions = (originalWidth, originalHeight, maxWidth, maxHeight) => {
  let width = originalWidth;
  let height = originalHeight;

  // Scale down if too large
  if (width > maxWidth) {
    height = (height * maxWidth) / width;
    width = maxWidth;
  }

  if (height > maxHeight) {
    width = (width * maxHeight) / height;
    height = maxHeight;
  }

  return { width: Math.round(width), height: Math.round(height) };
};

/**
 * Compress canvas to target file size
 */
const compressToTarget = async (canvas, format, initialQuality, maxSizeKB, originalName) => {
  let quality = initialQuality;
  let attempts = 0;
  const maxAttempts = 10;

  while (attempts < maxAttempts) {
    const blob = await new Promise(resolve => {
      canvas.toBlob(resolve, format, quality);
    });

    const sizeKB = blob.size / 1024;

    // If size is acceptable, return the file
    if (sizeKB <= maxSizeKB || quality <= 0.1) {
      const fileName = generateCompressedFileName(originalName, format);
      return new File([blob], fileName, { type: format });
    }

    // Reduce quality for next attempt
    quality *= 0.8;
    attempts++;
  }

  // If we can't meet the target, return the smallest version
  const blob = await new Promise(resolve => {
    canvas.toBlob(resolve, format, 0.1);
  });

  const fileName = generateCompressedFileName(originalName, format);
  return new File([blob], fileName, { type: format });
};

/**
 * Generate appropriate filename for compressed image
 */
const generateCompressedFileName = (originalName, format) => {
  const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '');
  const extension = format === 'image/jpeg' ? 'jpg' :
                   format === 'image/png' ? 'png' :
                   format === 'image/webp' ? 'webp' : 'jpg';

  return `${nameWithoutExt}_compressed.${extension}`;
};

/**
 * Get optimal compression settings based on image type
 * Preserves original format when possible (WebP, PNG, JPG)
 */
export const getCompressionSettings = (imageType, originalFormat = 'image/jpeg') => {
  // Preserve WebP format if original is WebP, otherwise use JPEG for better compression
  const format = originalFormat === 'image/webp' ? 'image/webp' : 'image/jpeg';

  const settings = {
    banner: {
      maxWidth: 1200,
      maxHeight: 400,
      quality: 0.85,
      maxSizeKB: 400,
      format: format
    },
    avatar: {
      maxWidth: 300,
      maxHeight: 300,
      quality: 0.9,
      maxSizeKB: 100,
      format: format
    },
    product: {
      maxWidth: 800,
      maxHeight: 600,
      quality: 0.85,
      maxSizeKB: 300,
      format: format
    },
    default: {
      maxWidth: 1200,
      maxHeight: 800,
      quality: 0.8,
      maxSizeKB: 500,
      format: format
    }
  };

  return settings[imageType] || settings.default;
};

/**
 * Validate image file before compression
 */
export const validateImageFile = (file) => {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  const maxSizeMB = 25; // 25MB limit

  if (!validTypes.includes(file.type)) {
    throw new Error('Tipo de archivo no válido. Use JPG, PNG o WebP.');
  }

  if (file.size > maxSizeMB * 1024 * 1024) {
    throw new Error(`El archivo es demasiado grande. Máximo ${maxSizeMB}MB.`);
  }

  return true;
};

/**
 * Get file size in human readable format
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Preview compressed image before upload
 */
export const createImagePreview = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};