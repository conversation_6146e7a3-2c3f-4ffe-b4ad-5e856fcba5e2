import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Calendar, 
  Users, 
  Building2,
  Menu, 
  X 
} from 'lucide-react';

const MobileMenuButton = styled(motion.button)`
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 9999;
  background: #F16925;
  border: none;
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  color: #fff;
  display: none;

  @media (max-width: 1024px) {
    display: block;
    padding: 0.4rem;
    top: 0.8rem;
    left: 0.8rem;
  }

  @media (max-width: 480px) {
    padding: 0.3rem;
    top: 0.6rem;
    left: 0.6rem;
  }
`;

const Overlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9998;
  display: none;

  @media (max-width: 1024px) {
    display: block;
  }
`;

const MobileMenuContainer = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 80%;
  max-width: 300px;
  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 9999;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  display: none;

  @media (max-width: 1024px) {
    display: flex;
    width: 85%;
    padding: 1.2rem;
  }

  @media (max-width: 480px) {
    width: 90%;
    padding: 1rem;
  }
`;

const Logo = styled.img`
  width: 50px;
  height: 50px;
  margin-bottom: 2rem;

  @media (max-width: 480px) {
    width: 40px;
    height: 40px;
    margin-bottom: 1.5rem;
  }
`;

const LogoSubtitle = styled.p`
  font-size: 0.9rem;
  color: #94a3b8;
  text-align: center;
  margin: 0 0 2rem 0;
  font-weight: 500;
`;

const NavList = styled.nav`
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
  margin-top: 1rem;

  @media (max-width: 480px) {
    gap: 0.5rem;
  }
`;

const NavItem = styled(Link)`
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1rem;
  color: ${props => props.$active ? '#F16925' : '#e2e8f0'};
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  background: ${props => props.$active ? 'rgba(241, 105, 37, 0.12)' : 'transparent'};
  border: 1px solid ${props => props.$active ? '#F16925' : 'transparent'};

  &:hover {
    background: rgba(241, 105, 37, 0.12);
    color: #F16925;
    transform: translateX(5px);
  }

  svg {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    color: ${props => props.$active ? '#F16925' : '#94a3b8'};
  }

  @media (max-width: 480px) {
    padding: 0.6rem 0.8rem;
    font-size: 0.85rem;
    gap: 0.8rem;

    svg {
      width: 18px;
      height: 18px;
    }
  }
`;

const BackButton = styled(Link)`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #F16925;
  color: #fff;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  margin-top: 2rem;
  transition: all 0.3s ease;
  width: 100%;
  border: none;

  &:hover {
    background: #e05a1a;
    transform: translateY(-2px);
  }

  svg {
    width: 20px;
    height: 20px;
    color: #fff;
  }

  @media (max-width: 480px) {
    padding: 0.6rem 0.8rem;
    font-size: 0.9rem;
    gap: 0.4rem;

    svg {
      width: 18px;
      height: 18px;
    }
  }
`;

const AdminMobileMenu = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const handleNavClick = (path) => {
    navigate(path);
    setIsOpen(false); // Close menu after navigation
  };

  const navItems = [
    { icon: <Calendar />, text: 'Events Management', path: '/admin/events' },
    { icon: <Users />, text: 'User Management', path: '/admin/users' },
    { icon: <Building2 />, text: 'Tenant Listings', path: '/admin/tenants' }
  ];

  return (
    <>
      <MobileMenuButton
        onClick={toggleMenu}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        {isOpen ? <X size={24} /> : <Menu size={24} />}
      </MobileMenuButton>

      <AnimatePresence>
        {isOpen && (
          <>
            <Overlay
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={toggleMenu}
            />
            <MobileMenuContainer
              initial={{ x: -300 }}
              animate={{ x: 0 }}
              exit={{ x: -300 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
            >
              <Logo src="/logo.webp" alt="Mecrado Logo" />
              <LogoSubtitle>Admin Panel</LogoSubtitle>

              <NavList>
                {navItems.map((item, index) => {
                  const isActive = location.pathname === item.path || 
                                  (item.path !== '/admin' && location.pathname.startsWith(item.path));
                  
                  return (
                    <NavItem
                      key={index}
                      to={item.path}
                      $active={isActive}
                      onClick={() => handleNavClick(item.path)}
                    >
                      {item.icon}
                      <span>{item.text}</span>
                    </NavItem>
                  );
                })}
              </NavList>

              <BackButton to="/">
                <span>Back to Home</span>
              </BackButton>
            </MobileMenuContainer>
          </>
        )}
      </AnimatePresence>
    </>
  );
};

export default AdminMobileMenu;
