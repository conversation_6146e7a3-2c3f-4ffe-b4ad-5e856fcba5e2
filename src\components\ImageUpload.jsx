import React, { useState } from 'react';
import styled from 'styled-components';
import { Camera } from 'lucide-react';
import { supabase } from '../utils/supabaseClient';

const UploadContainer = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: ${props => props.$isAvatar ? '50%' : '12px'};
  overflow: hidden;
  cursor: pointer;

  &:hover {
    .upload-button {
      opacity: 1;
    }
  }
`;

const ImagePreview = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const UploadButton = styled.label`
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px;
  border-radius: 50%;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;

  &:hover {
    background: rgba(0, 0, 0, 0.9);
  }
`;

const FileInput = styled.input`
  display: none;
`;

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 3;
`;

const ImageUpload = ({ currentImage, onImageUpload, type, userId, showPreview = true }) => {
  const [loading, setLoading] = useState(false);

  const handleFileChange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    try {
      setLoading(true);

      // Import compression utilities dynamically
      const { compressImage, getCompressionSettings, validateImageFile, formatFileSize } = await import('../utils/imageCompression');

      // Validate file before processing
      validateImageFile(file);

      console.log(`Original file size: ${formatFileSize(file.size)}`);

      // Get compression settings and preserve original format
      const compressionSettings = getCompressionSettings(type, file.type);
      console.log(`Original format: ${file.type}, Target format: ${compressionSettings.format}`);

      // Compress the image
      const compressedFile = await compressImage(file, compressionSettings);
      console.log(`Compressed file size: ${formatFileSize(compressedFile.size)}`);

      // Generate unique filename for compressed file with timestamp for consistency
      const fileExt = compressedFile.name.split('.').pop();
      const fileName = `${userId}/${type}-${Date.now()}.${fileExt}`;

      // Upload compressed image to Supabase Storage
      const { error: uploadError, data } = await supabase.storage
        .from('images')
        .upload(fileName, compressedFile, {
          upsert: true,
          cacheControl: '31536000' // 1 year cache for better performance
        });

      if (uploadError) {
        console.error('Error al subir:', uploadError);
        throw uploadError;
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('images')
        .getPublicUrl(fileName);

      // Call callback with public URL instead of file to prevent double upload
      onImageUpload(publicUrl);

    } catch (error) {
      console.error('Error al subir imagen:', error);
      alert('Error al subir la imagen: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <UploadContainer $isAvatar={type === 'avatar'}>
      {showPreview && currentImage && <ImagePreview src={currentImage} alt={type} />}
      <UploadButton className="upload-button">
        <Camera size={20} />
        <FileInput
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          disabled={loading}
        />
      </UploadButton>
      {loading && (
        <LoadingOverlay>
          Subiendo imagen...
        </LoadingOverlay>
      )}
    </UploadContainer>
  );
};

export default ImageUpload; 