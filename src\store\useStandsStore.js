import { create } from 'zustand';
import { supabase } from '../utils/supabaseClient';

// Request deduplication
let ongoingRequest = null;
let requestPromise = null;

// Debounce utility
const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(null, args), delay);
  };
};

// Function for fetching stands (cache removed to fix duplicate issue)
const fetchStandsData = async (bypassCache = false) => {
  const { data, error } = await supabase
    .from('stands')
    .select(`
      id,
      name,
      banner_url,
      avatar,
      slug,
      category,
      description,
      unit,
      unit_type,
      phone_default
    `)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data || [];
};

export const useStandsStore = create((set, get) => ({
  stands: [],
  loading: false,
  error: null,
  lastFetch: null,
  searchTerm: '',
  filteredStands: [],

  // Debounced search function
  debouncedSearch: debounce((searchTerm, stands) => {
    if (!searchTerm.trim()) {
      set({ filteredStands: stands, searchTerm });
      return;
    }

    const filtered = stands.filter((stand) => {
      const term = searchTerm.toLowerCase();
      return (
        stand.name.toLowerCase().includes(term) ||
        stand.category?.toLowerCase().includes(term) ||
        stand.description?.toLowerCase().includes(term) ||
        stand.description_es?.toLowerCase().includes(term) ||
        stand.description_en?.toLowerCase().includes(term)
      );
    });

    set({ filteredStands: filtered, searchTerm });
  }, 300), // 300ms debounce delay

  // Search function that triggers debounced search
  searchStands: (searchTerm) => {
    const { stands } = get();
    get().debouncedSearch(searchTerm, stands);
  },

  // Clear search
  clearSearch: () => {
    const { stands } = get();
    set({ searchTerm: '', filteredStands: stands });
  },

  fetchStands: async () => {
    // Request deduplication - prevent multiple simultaneous calls
    if (ongoingRequest) {
      return ongoingRequest;
    }
    
    set({ loading: true, error: null });
    
    // Create promise for deduplication
    requestPromise = (async () => {
      try {
        const data = await fetchStandsData();
        
        set({ 
          stands: data, 
          filteredStands: data, // Initialize filtered with all data
          loading: false, 
          lastFetch: new Date().toISOString()
        });
        
        return data;
        
      } catch (error) {
        set({ 
          stands: [], 
          filteredStands: [],
          error: 'Error al cargar los stands', 
          loading: false 
        });
        throw error;
      } finally {
        // Clear ongoing request
        ongoingRequest = null;
        requestPromise = null;
      }
    })();
    
    ongoingRequest = requestPromise;
    return requestPromise;
  },

  // Force refresh (bypasses cache)
  refreshStands: async () => {
    // Clear any ongoing request
    ongoingRequest = null;
    requestPromise = null;

    // Force fresh data fetch
    set({ loading: true, error: null });

    try {
      const data = await fetchStandsData(true); // Pass bypassCache flag

      set({
        stands: data,
        filteredStands: data,
        loading: false,
        lastFetch: new Date().toISOString()
      });

      return data;

    } catch (error) {
      set({
        stands: [],
        filteredStands: [],
        error: 'Error al cargar los stands',
        loading: false
      });
      throw error;
    }
  },

  // Get cache status
  getCacheStatus: () => {
    const { lastFetch } = get();
    return {
      hasReactCache: true,
      cacheType: 'React built-in cache + Request deduplication',
      invalidation: 'Per server request',
      lastFetch,
      requestDeduplication: 'Active',
      debouncing: 'Active (300ms)',
      note: 'Cached stands data with debounced search'
    };
  },

  // Clear cache and force fresh data
  clearCache: () => {
    ongoingRequest = null;
    requestPromise = null;
    set({ lastFetch: null });
  },
}));
