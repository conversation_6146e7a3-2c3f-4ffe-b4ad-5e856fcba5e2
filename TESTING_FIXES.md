# Testing Image Upload Fixes

## Issues Fixed

### ✅ Issue 1: WebP → JPG Conversion
**Problem**: WebP images were being converted to JPG during compression
**Solution**: Modified compression settings to preserve original format when possible

### ✅ Issue 2: Old Images Not Being Deleted
**Problem**: Storage bucket accumulating multiple images instead of replacing them
**Solution**: Fixed URL path extraction logic in deletion functions

### ✅ Issue 3: Double Egress Charges During Upload
**Problem**: Images being downloaded twice during upload process causing double billing
**Solution**: Optimized upload sequence and timing to prevent unnecessary downloads

## Testing Instructions

### 1. Test WebP Format Preservation
1. **Prepare a WebP image** (you can convert any image to WebP online)
2. **Upload it as banner** in Edit Profile → Edit Stand
3. **Check browser console** for these messages:
   ```
   Original format: image/webp, Target format: image/webp
   Compressed banner size: [size]
   ```
4. **Check Supabase storage** - file should have `.webp` extension

### 2. Test Image Replacement (Banner)
1. **Upload initial banner** and note the filename in storage
2. **Upload different banner** 
3. **Check console** for deletion messages:
   ```
   Attempting to delete old image: [old-url]
   Extracted file path for deletion: [user-id]/banner-[timestamp].webp
   ✅ Old image deleted successfully: [file-path]
   ```
4. **Verify storage** - only newest banner should exist

### 3. Test Image Replacement (Products)
1. **Create/edit a product** with image
2. **Upload different product image**
3. **Check console** for:
   ```
   Attempting to delete old product image: [old-url]
   Extracted product file path for deletion: [user-id]/products/[timestamp].webp
   ✅ Old product image deleted successfully: [file-path]
   ```

### 4. Test Avatar Replacement
1. **Upload avatar** in profile
2. **Upload different avatar**
3. **Verify** old avatar is deleted

## Expected Console Output

### Successful Upload with Replacement (Optimized Sequence):
```
Original format: image/webp, Target format: image/webp
Original banner size: 2.5 MB
Compressed banner size: 387 KB
🗑️ Deleting old image BEFORE upload to prevent double egress
Attempting to delete old image: https://[project].supabase.co/storage/v1/object/public/images/[user-id]/banner-1754197040.webp
Extracted file path for deletion: [user-id]/banner-1754197040.webp
✅ Old image deleted successfully: [user-id]/banner-1754197040.webp
⏳ Waiting 500ms before UI update to optimize egress usage
```

### Error Scenarios to Watch For:
```
❌ Supabase deletion error: [error details]
❌ Could not extract file path from URL: [url]
❌ Failed to delete file path: [path]
```

## Verification Queries

### Check Current Storage State:
```sql
-- See all files in storage
SELECT name, (metadata->>'size')::int / 1024 as size_kb, created_at
FROM storage.objects 
WHERE bucket_id = 'images' 
ORDER BY created_at DESC;

-- Count files per user (should be minimal after cleanup)
SELECT 
  split_part(name, '/', 1) as user_id,
  COUNT(*) as file_count,
  SUM((metadata->>'size')::int) / 1024 as total_kb
FROM storage.objects 
WHERE bucket_id = 'images'
GROUP BY split_part(name, '/', 1)
ORDER BY file_count DESC;
```

## Success Criteria

✅ **WebP files stay as WebP** (not converted to JPG)
✅ **Only 1 banner per stand** in storage
✅ **Only 1 image per product** in storage
✅ **Only 1 avatar per user** in storage
✅ **Console shows successful deletions**
✅ **No accumulation of old files**
✅ **No double egress charges** (old image deleted BEFORE new upload)
✅ **Optimized upload sequence** (delete → upload → delay → UI update)

## If Issues Persist

1. **Check browser console** for specific error messages
2. **Verify Supabase storage permissions** for delete operations
3. **Test with different image formats** (WebP, JPG, PNG)
4. **Check network tab** for failed API calls to storage endpoints
