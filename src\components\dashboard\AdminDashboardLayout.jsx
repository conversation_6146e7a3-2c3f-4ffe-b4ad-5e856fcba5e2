import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useUserStore } from '../../store/useUserStore';
import { useAuthStore } from '../../store/useAuthStore';
import { Navigate } from 'react-router-dom';
import AdminSidebar from './AdminSidebar';
import AdminTopNavbar from './AdminTopNavbar';
import AdminMobileMenu from './AdminMobileMenu';
import LoadingSpinner from '../LoadingSpinner';

const AdminContainer = styled(motion.div)`
  display: flex;
  min-height: 100vh;
  background: #f8f9fa;
`;

const AdminMainContent = styled(motion.main)`
  flex: 1;
  background: #f8fafc;
  min-height: 100vh;
  margin-left: 280px;
  padding-top: 80px;
  transition: margin-left 0.3s ease;

  @media (max-width: 1024px) {
    margin-left: 0;
    padding-top: 80px;
    width: 100%;
  }

  @media (max-width: 768px) {
    padding: 1rem;
    padding-top: 80px;
  }
`;

const MobileOverlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
  display: none;
  cursor: pointer;

  @media (max-width: 1024px) {
    display: ${props => props.$isOpen ? 'block' : 'none'};
  }
`;

const AdminDashboardLayout = ({ children }) => {
  const { userData, role, isAdmin, loading: userStoreLoading, fetchUserData } = useUserStore();
  const { user: authUser, isLoading: authLoading, initialized: authInitialized } = useAuthStore();

  useEffect(() => {
    // If auth is initialized and user is present but user data isn't loaded, fetch it
    if (authInitialized && authUser && !userData && !userStoreLoading) {
      fetchUserData(authUser.id);
    }
  }, [authInitialized, authUser, userData, userStoreLoading, fetchUserData]);

  // Show loading spinner while authentication and user data are being fetched
  if (authLoading || userStoreLoading || !authInitialized) {
    return <LoadingSpinner message="Verificando permisos de administrador..." />;
  }

  // Check if user is admin (role = 'admin' AND is_admin = true)
  // If not authenticated or not admin, redirect to home
  if (!authUser || !userData || role !== 'admin' || !isAdmin) {
    return <Navigate to="/" replace />;
  }

  return (
    <AdminContainer
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {/* Desktop sidebar - hidden on mobile */}
      <AdminSidebar />
      
      {/* Mobile menu - shown on mobile/tablet */}
      <AdminMobileMenu />
      
      <AdminTopNavbar />
      <AdminMainContent
        initial={{ x: 20, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        {children}
      </AdminMainContent>
    </AdminContainer>
  );
};

export default AdminDashboardLayout;
