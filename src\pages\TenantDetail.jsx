import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import styled from 'styled-components';
import { supabase } from '../utils/supabaseClient';
import Loader from '../components/Loader';
import { ArrowLeft, Building2, Tag, Phone, Shapes } from 'lucide-react';
import { useTenantListingsStore } from '../store/useTenantListingsStore';
import { useLanguageStore } from '../store/useLanguageStore';
import UnitsDisplay from '../components/UnitsDisplay';

const Wrapper = styled.div`
  background:#f8f9fa; min-height:100vh;
`;
const Container = styled.div`
  max-width:900px;
  margin:0 auto;
  padding:2rem 1rem 4rem 1rem;
  display:flex;
  flex-direction:column;
  align-items:center;
  text-align:center;

  @media (max-width: 768px) {
    padding: 1.5rem 1rem 3rem 1rem;
  }
`;
const Banner = styled.img`
  width:100%;
  height:280px;
  object-fit:cover;
  object-position: center;
  border-radius:24px;
  box-shadow:0 10px 40px rgba(0,0,0,0.1);

  @media (max-width: 768px) {
    height: 200px;
    border-radius: 16px;
  }
`;
const Avatar = styled.img`
  width:150px;
  height:150px;
  border-radius:50%;
  margin-top:-75px;
  border:6px solid #fff;
  background:#fff;
  box-shadow:0 5px 20px rgba(0,0,0,0.15);
  object-fit: cover;

  @media (max-width: 768px) {
    width: 120px;
    height: 120px;
    margin-top: -60px;
  }
`;

const InfoGrid = styled.div`
  margin-top:1.5rem;
  display:grid;
  gap:1.5rem;
  width:100%;
  grid-template-columns: repeat(auto-fit,minmax(250px,1fr));

  @media (max-width: 768px) {
    gap: 1rem;
    margin-top: 1rem;
  }
`;

const InfoBox = styled.div`
  background:#fff;
  border-radius:12px;
  padding:1.25rem 1.5rem;
  box-shadow:0 4px 15px rgba(0,0,0,0.06);
  display:flex;
  align-items:center;
  gap:1rem;
  transition: all 0.2s ease-in-out;
  border: 1px solid #e9ecef;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
  }
`;

const IconWrapper = styled.div`
  color: #F16925;
`;

const CardContent = styled.div`
  text-align: left;
`;

const Label = styled.span`
  display:block;
  font-size:0.8rem;
  font-weight:600;
  color:#6c757d;
  text-transform:uppercase;
  letter-spacing:0.5px;
  margin-bottom:0.25rem;
`;

const Value = styled.span`
  font-size:1.05rem;
  color:#343a40;
  font-weight: 500;

  a {
    color: #343a40;
    text-decoration: none;
    font-weight: 600;
    &:hover {
      color: #F16925;
    }
  }
`;

const Title = styled.h1`
  margin-top: ${({ hasAvatar }) => (hasAvatar ? '1.5rem' : '3rem')};
  margin-bottom: 1.5rem;
  color:#212529;
  font-size: 2.5rem;
  font-weight: 700;

  @media (max-width: 768px) {
    font-size: 2rem;
    margin-top: ${({ hasAvatar }) => (hasAvatar ? '1rem' : '2rem')};
    margin-bottom: 1rem;
  }
`;

const Back = styled(Link)`
  display:inline-flex;
  align-items:center;
  gap:8px;
  margin-bottom:2rem;
  color:#495057;
  text-decoration:none;
  font-weight:600;
  padding: 0.5rem 1rem;
  background: #fff;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  transition: all 0.2s ease;

  &:hover {
    color: #F16925;
    box-shadow: 0 4px 8px rgba(0,0,0,0.07);
    transform: translateY(-2px);
  }

  @media (max-width: 768px) {
    width: 100%;
    justify-content: center;
    padding: 0.75rem 1rem;
  }
`;

// helper to create url-friendly slugs (keep in sync with StandsVirtuales)
const slugify = (str) =>
  str
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');

const safeSlug = (text, fallback) => {
  const s = slugify(text || '');
  return s.length ? s : String(fallback);
};

// Utility to safely get a field value even if column name has unexpected spaces or casing
const getFieldValue = (obj, baseKey) => {
  if (!obj) return null;
  // direct match
  if (obj[baseKey] && String(obj[baseKey]).trim() !== '') return obj[baseKey];
  // look for keys that include the baseKey without special chars (e.g., 'phone_default ')
  const normalized = baseKey.toLowerCase().replace(/[^a-z]/g, '');
  const foundKey = Object.keys(obj).find((k) =>
    k.toLowerCase().replace(/[^a-z]/g, '').startsWith(normalized),
  );
  const val = foundKey ? obj[foundKey] : null;
  return val && String(val).trim() !== '' ? val : null;
};

const TenantDetail = () => {
  const { slug } = useParams();
  // NEW – tap into global tenant listings store
  const { tenants, fetchTenants } = useTenantListingsStore();
  const { language } = useLanguageStore();
  const [tenant, setTenant] = useState(null);
  const [loading, setLoading] = useState(true);

  // i18n labels
  const t = {
    back: language === 'es' ? 'Atrás' : 'Back',
    unit: language === 'es' ? 'Unidad' : 'Unit',
    type: language === 'es' ? 'Tipo' : 'Type',
    phone: language === 'es' ? 'Teléfono' : 'Phone',
    category: language === 'es' ? 'Categoría' : 'Category',
    notFound: language === 'es' ? 'No encontrado' : 'Not found',
    na: language === 'es' ? 'N/D' : 'N/A',
  };

  // Ensure we have tenant listings loaded
  useEffect(() => {
    if (!tenants.length) {
      fetchTenants();
    }
  }, [tenants.length, fetchTenants]);

  // Find tenant once listings are available or slug changes
  useEffect(() => {
    if (tenants.length) {
      const found = tenants.find(
        (t) => safeSlug(t.slug || t.tenant_name, t.id) === slug,
      );
      setTenant(found || null);
      setLoading(false);
      
      // Debug logging to see what data we have
      if (found) {
        console.log('Found tenant data:', found);
        console.log('unit_type:', found.unit_type);
        console.log('phone_default:', found.phone_default);
      }
    }
  }, [tenants, slug]);

  // Fallback single fetch (keeps previous logic but only runs if still not found)
  useEffect(() => {
    const fetchSingle = async () => {
      if (tenant || !loading) return;
      const { data } = await supabase
        .from('tenant_listings')
        .select('*')
        .ilike('tenant_name', `%${slug.replace(/-/g,' ')}%`) // loose match
        .maybeSingle();
      if (data) {
        setTenant(data);
        // Debug logging for fallback fetch
        console.log('Fallback fetch tenant data:', data);
        console.log('unit_type:', data.unit_type);
        console.log('phone_default:', data.phone_default);
      }
      setLoading(false);
    };
    fetchSingle();
  }, [tenant, loading, slug]);

  if (loading) return <Loader />;
  if (!tenant) return <Wrapper><Container>{t.notFound}</Container></Wrapper>;

  const typeValue = getFieldValue(tenant, 'unit_type') || (tenant.category && tenant.category.trim() !== '' ? tenant.category : t.na);
  const phoneValue = getFieldValue(tenant, 'phone_default') || getFieldValue(tenant, 'phone') || getFieldValue(tenant, 'whatsapp');
  const avatarUrl = getFieldValue(tenant, 'avatar');

  return (
    <Wrapper>
      <Container>
        <Back to="/stands-virtuales"><ArrowLeft size={18}/>{t.back}</Back>
        <Banner src={getFieldValue(tenant, 'banner_url') || '/logo.webp'} alt={tenant.tenant_name}/>
        {avatarUrl && <Avatar src={avatarUrl} alt={tenant.tenant_name}/>}
        <Title hasAvatar={!!avatarUrl}>{tenant.tenant_name}</Title>
        <InfoGrid>
          <InfoBox>
            <IconWrapper><Building2 size={24} /></IconWrapper>
            <CardContent>
              <Label>{t.unit}</Label>
              <Value>
                <UnitsDisplay
                  units={getFieldValue(tenant, 'units')}
                  fallbackUnit={getFieldValue(tenant, 'unit')}
                  variant="text"
                  showIcon={false}
                  language={language}
                />
              </Value>
            </CardContent>
          </InfoBox>
          <InfoBox>
            <IconWrapper><Tag size={24} /></IconWrapper>
            <CardContent>
              <Label>{t.type}</Label>
              <Value>{typeValue}</Value>
            </CardContent>
          </InfoBox>
          <InfoBox>
            <IconWrapper><Phone size={24} /></IconWrapper>
            <CardContent>
              <Label>{t.phone}</Label>
              <Value>
                {phoneValue ? (
                  <a href={`tel:${phoneValue}`}>{phoneValue}</a>
                ) : t.na}
              </Value>
            </CardContent>
          </InfoBox>
          {tenant.category && (
            <InfoBox>
              <IconWrapper><Shapes size={24} /></IconWrapper>
              <CardContent>
                <Label>{t.category}</Label>
                <Value>{tenant.category}</Value>
              </CardContent>
            </InfoBox>
          )}
        </InfoGrid>
              </Container>
    </Wrapper>
  );
};

export default TenantDetail; 