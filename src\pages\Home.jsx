import Hero from '../components/Hero';
import CallToAction from '../components/CallToAction';
import styled from 'styled-components';
import { useLanguageStore } from '../store/useLanguageStore';
import About from '../components/About';
import Top10Stands from '../components/Top10Stands';
import InstagramWidget from '../components/InstagramWidget';
import HomeEvents from '../components/HomeEvents';



const CategoryGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  border-top: 1px solid #7a3a1a;
  border-bottom: 1px solid #7a3a1a;
  background: #f8f6ea;
  @media (max-width: 900px) {
    grid-template-columns: 1fr 1fr;
  }
  @media (max-width: 600px) {
    grid-template-columns: 1fr;
  }
`;

const CategoryBox = styled.div`
  padding: 1.5rem 0.5rem 1rem 0.5rem;
  text-align: center;
  border-right: 1px solid #7a3a1a;
  @media (max-width: 900px) {
    border-right: none;
    border-bottom: 1px solid #7a3a1a;
    &:last-child {
      border-bottom: none;
    }
  }
  &:last-child {
    border-right: none;
  }
`;

const CategoryTitle = styled.div`
  color: #d94d2a;
  font-weight: 700;
  font-size: 2rem;
  letter-spacing: 1px;
  margin-bottom: 0.3rem;
`;

const CategoryDesc = styled.div`
  color: #2d1a0b;
  font-weight: 700;
  font-size: 1rem;
  letter-spacing: 1px;
`;

export default function Home() {
  const { language } = useLanguageStore();

  const categories = [
    {
      title: language === 'es' ? 'Comida' : 'Eat',
      desc: language === 'es' ? 'Explora sitios de comida ' : 'Explore the Restaurants',
    },
    {
      title: language === 'es' ? 'Tienda' : 'Shop',
      desc: language === 'es' ? 'Compra productos únicos' : 'Shop unique products',
    },
    {
      title: language === 'es' ? 'Belleza' : 'Beauty',
      desc: language === 'es' ? 'Descubre la locales de Belleza' : 'Discover local Beauty',
    },
  ];

  return (
    <div>
      <Hero />
      <CategoryGrid>
        {categories.map((cat, idx) => (
          <CategoryBox key={idx}>
            <CategoryTitle>{cat.title}</CategoryTitle>
            <CategoryDesc>{cat.desc}</CategoryDesc>
          </CategoryBox>
        ))}
      </CategoryGrid>
      <HomeEvents />
      <Top10Stands />
      <InstagramWidget />
      <About />
      <CallToAction />
    </div>
  );
}
