import { create } from 'zustand';
import { supabase } from '../utils/supabaseClient';

// Debounce utility
const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(null, args), delay);
  };
};

export const useTenantListingsStore = create((set, get) => ({
  tenants: [],
  loading: false,
  error: null,
  searchTerm: '',
  filteredTenants: [],

  // Debounced search function
  debouncedSearch: debounce((searchTerm, tenants) => {
    if (!searchTerm.trim()) {
      set({ filteredTenants: tenants, searchTerm });
      return;
    }

    const filtered = tenants.filter((tenant) => {
      const term = searchTerm.toLowerCase();
      return (
        tenant.tenant_name.toLowerCase().includes(term) ||
        tenant.category?.toLowerCase().includes(term) ||
        tenant.description?.toLowerCase().includes(term) ||
        tenant.description_es?.toLowerCase().includes(term) ||
        tenant.description_en?.toLowerCase().includes(term)
      );
    });

    set({ filteredTenants: filtered, searchTerm });
  }, 300), // 300ms debounce delay

  // Search function that triggers debounced search
  searchTenants: (searchTerm) => {
    const { tenants } = get();
    get().debouncedSearch(searchTerm, tenants);
  },

  // Clear search
  clearSearch: () => {
    const { tenants } = get();
    set({ searchTerm: '', filteredTenants: tenants });
  },

  fetchTenants: async () => {
    try {
      set({ loading: true, error: null });
      const { data, error } = await supabase
        .from('tenant_listings')
        .select('*')
        .order('tenant_name', { ascending: true });
      if (error) throw error;
      set({ 
        tenants: data || [], 
        filteredTenants: data || [], // Initialize filtered with all data
        loading: false 
      });
    } catch {
      set({ 
        error: 'Error loading tenant listings', 
        loading: false,
        tenants: [],
        filteredTenants: []
      });
    }
  },

  createTenant: async (tenantData) => {
    try {
      set({ loading: true, error: null });

      // Ensure units is properly formatted as an array
      const formattedData = {
        ...tenantData,
        units: Array.isArray(tenantData.units)
          ? tenantData.units.filter(unit => unit && unit.trim() !== '')
          : tenantData.units ? [tenantData.units.toString().trim()] : []
      };

      const { data, error } = await supabase
        .from('tenant_listings')
        .insert([formattedData])
        .select()
        .single();

      if (error) throw error;

      // Add the new tenant to the store
      const currentTenants = get().tenants;
      const updatedTenants = [...currentTenants, data].sort((a, b) => a.tenant_name.localeCompare(b.tenant_name));
      set({
        tenants: updatedTenants,
        filteredTenants: updatedTenants, // Update filtered tenants too
        loading: false
      });

      return { data, error: null };
    } catch (error) {
      set({ error: error.message, loading: false });
      return { data: null, error: error.message };
    }
  },

  updateTenant: async (id, tenantData) => {
    try {
      set({ loading: true, error: null });

      // Ensure units is properly formatted as an array
      const formattedData = {
        ...tenantData,
        units: Array.isArray(tenantData.units)
          ? tenantData.units.filter(unit => unit && unit.trim() !== '')
          : tenantData.units ? [tenantData.units.toString().trim()] : []
      };

      const { data, error } = await supabase
        .from('tenant_listings')
        .update(formattedData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      // Update the tenant in the store
      const currentTenants = get().tenants;
      const updatedTenants = currentTenants.map(tenant =>
        tenant.id === id ? data : tenant
      ).sort((a, b) => a.tenant_name.localeCompare(b.tenant_name));

      set({ 
        tenants: updatedTenants, 
        filteredTenants: updatedTenants, // Update filtered tenants too
        loading: false 
      });

      return { data, error: null };
    } catch (error) {
      set({ error: error.message, loading: false });
      return { data: null, error: error.message };
    }
  },

  deleteTenant: async (id) => {
    try {
      set({ loading: true, error: null });

      const { error } = await supabase
        .from('tenant_listings')
        .delete()
        .eq('id', id);

      if (error) throw error;

      // Remove the tenant from the store
      const currentTenants = get().tenants;
      const filteredTenants = currentTenants.filter(tenant => tenant.id !== id);

      set({ 
        tenants: filteredTenants, 
        filteredTenants: filteredTenants, // Update filtered tenants too
        loading: false 
      });

      return { error: null };
    } catch (error) {
      set({ error: error.message, loading: false });
      return { error: error.message };
    }
  },

  clearError: () => set({ error: null }),
}));