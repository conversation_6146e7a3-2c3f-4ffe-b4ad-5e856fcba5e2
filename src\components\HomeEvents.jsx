import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { useLanguageStore } from "../store/useLanguageStore";
import { supabase } from "../utils/supabaseClient";
import { FaCalendarAlt, FaMapMarkerAlt, FaClock } from "react-icons/fa";
import { useNavigate } from "react-router-dom";

const EventsSection = styled.section`
  padding: 3rem 1.5rem;
  background: linear-gradient(135deg, #f8f6ea 0%, #fff5e6 100%);
  border-top: 2px solid #7a3a1a;
  border-bottom: 2px solid #7a3a1a;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(
      circle at 1px 1px,
      rgba(122, 58, 26, 0.03) 1px,
      transparent 0
    );
    background-size: 20px 20px;
    pointer-events: none;
  }
`;

const SectionSubtitle = styled.p`
  text-align: center;
  color: #2d1a0b;
  font-size: 1rem;
  margin-bottom: 2rem;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.4;
`;

const EventsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.2rem;
  max-width: 1000px;
  margin: 0 auto;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 0.8rem;
  }
`;

const EventCard = styled.div`
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(122, 58, 26, 0.12);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(122, 58, 26, 0.1);

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(122, 58, 26, 0.2);
    border-color: #d94d2a;
  }
`;

const EventImage = styled.div`
  height: 250px;
  background: ${(props) =>
    props.imageUrl
      ? `url(${props.imageUrl})`
      : "linear-gradient(135deg, #d94d2a, #7a3a1a)"};
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  position: relative;
  border-bottom: 3px solid rgba(122, 58, 26, 0.2);
  box-shadow: inset 0 0 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      45deg,
      rgba(122, 58, 26, 0.02),
      rgba(217, 77, 42, 0.02)
    );
  }

  ${EventCard}:hover & {
    box-shadow: inset 0 0 35px rgba(0, 0, 0, 0.12);
  }
`;

const EventContent = styled.div`
  padding: 0.8rem;
`;

const EventTitle = styled.h3`
  color: #2d1a0b;
  font-size: 1rem;
  font-weight: 700;
  margin-bottom: 0.2rem;
  line-height: 1.2;
`;

const EventDescription = styled.p`
  color: #5a4a3a;
  font-size: 0.8rem;
  line-height: 1.3;
  margin-bottom: 0.4rem;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const EventMeta = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
  margin-bottom: 0.4rem;
`;

const MetaItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.3rem;
  color: #7a3a1a;
  font-size: 0.8rem;
  font-weight: 500;
`;

const MetaIcon = styled.span`
  color: #d94d2a;
  font-size: 0.8rem;
  min-width: 14px;
`;

const NoEvents = styled.div`
  text-align: center;
  padding: 3rem;
  color: #7a3a1a;
  font-size: 1.1rem;
`;

const ButtonContainer = styled.div`
  display: flex;
  gap: 0.6rem;
  margin-top: 0.5rem;
  justify-content: center;
`;

const GetTicketsButton = styled.button`
  background: #d94d2a;
  color: white;
  border: 1px solid #d94d2a;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 120px;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(217, 77, 42, 0.15);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(217, 77, 42, 0.1);
  }
`;

const ViewMoreButton = styled.button`
  background: white;
  color: #d94d2a;
  border: 1px solid #d94d2a;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 120px;

  &:hover {
    background: #d94d2a;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(217, 77, 42, 0.15);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(217, 77, 42, 0.1);
  }
`;

const ViewAllButton = styled.button`
  background: transparent;
  color: #d94d2a;
  border: 2px solid #d94d2a;
  padding: 0.8rem 1.8rem;
  border-radius: 25px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 1.5rem auto 0;
  display: block;

  &:hover {
    background: #d94d2a;
    color: white;
    transform: translateY(-2px);
  }
`;
//#homeevents
export default function HomeEvents() {
  const { language } = useLanguageStore();
  const navigate = useNavigate();
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchEvents();
  }, []);

  const fetchEvents = async () => {
    try {
      const { data, error } = await supabase
        .from("events")
        .select("*")
        .eq("status", "active")
        .order("start_date", { ascending: true })
        .limit(6);

      if (error) throw error;
      setEvents(data || []);
    } catch (error) {
      console.error("Error fetching events:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(language === "es" ? "es-ES" : "en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatTime = (timeString) => {
    if (!timeString) return "";
    return timeString.substring(0, 5);
  };

  // Hide entire section if no active events
  if (loading) {
    return null; // Don't show loading state, just hide section
  }

  // Hide entire section if no events
  if (!events || events.length === 0) {
    return null; // Return null to hide the entire section
  }

  return (
    <EventsSection>
      <SectionSubtitle>
        {language === "es"
          ? "Descubre los eventos especiales en nuestro mercado"
          : "Discover special events at our market"}
      </SectionSubtitle>

      <EventsGrid>
        {events.map((event) => (
          <EventCard key={event.id}>
            <EventImage imageUrl={event.image_url || event.banner_url} />
            <EventContent>
              <EventTitle>
                {language === "es"
                  ? event.title_es || event.title
                  : event.title_en || event.title}
              </EventTitle>
              <EventDescription>
                {language === "es"
                  ? event.description_es || event.description
                  : event.description_en || event.description}
              </EventDescription>

              <EventMeta>
                <MetaItem>
                  <MetaIcon>
                    <FaCalendarAlt />
                  </MetaIcon>
                  {formatDate(event.start_date)}
                </MetaItem>
                {event.start_time && (
                  <MetaItem>
                    <MetaIcon>
                      <FaClock />
                    </MetaIcon>
                    {formatTime(event.start_time)}
                  </MetaItem>
                )}
                {event.location && (
                  <MetaItem>
                    <MetaIcon>
                      <FaMapMarkerAlt />
                    </MetaIcon>
                    {language === "es"
                      ? event.location_es || event.location
                      : event.location_en || event.location}
                  </MetaItem>
                )}
              </EventMeta>
              
              <ButtonContainer>
                {event.link ? (
                  <GetTicketsButton 
                    onClick={() => window.open(event.link, '_blank')}
                  >
                    {language === "es" ? "Más Información" : "More Info"}
                  </GetTicketsButton>
                ) : (
                  <GetTicketsButton 
                    onClick={() => window.open('https://www.ticketleap.events/tickets/mercadofoodhall/foodiefest-2025?fbclid=IwY2xjawMSww9leHRuA2FlbQIxMABicmlkETFzS251M0ZWZlVwMU5jYWpkAR7qZXoh8uIecuk1ChmlJhMtUuExuLPaMK9JajkAMJy4h7kIFNURTo8Jkn8ZyQ_aem_4dtMsT0R2-CXaVJyRg87Ug', '_blank')}
                  >
                    {language === "es" ? "Obtener Entradas" : "Get Tickets"}
                  </GetTicketsButton>
                )}
                
                <ViewMoreButton 
                  onClick={() => navigate(`/eventinfo/${event.id}`)}
                >
                  {language === "es" ? "Ver Más" : "View More"}
                </ViewMoreButton>
              </ButtonContainer>
            </EventContent>
          </EventCard>
        ))}
      </EventsGrid>

      <ViewAllButton onClick={() => navigate("/events")}>
        {language === "es" ? "Ver Todos los Eventos" : "View All Events"}
      </ViewAllButton>
    </EventsSection>
  );
}
