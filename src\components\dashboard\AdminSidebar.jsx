import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { 
  Calendar, 
  Users,
  Building2
} from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';

const SidebarContainer = styled(motion.aside)`
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  width: 280px;
  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
  color: white;
  z-index: 1001;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  display: none;

  @media (min-width: 1025px) {
    display: block;
  }
`;

const Logo = styled.img`
  height: 50px;
  width: auto;
  margin: 0;
  display: block;
`;

const LogoSection = styled.div`
  padding: 2rem 2rem 2rem 2rem;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  margin-bottom: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const LogoSubtitle = styled.p`
  font-size: 0.9rem;
  color: #94a3b8;
  text-align: center;
  margin: 0.5rem 0 0 0;
`;

const NavSection = styled.div`
  padding: 0 1.5rem;
`;

const NavTitle = styled.h3`
  font-size: 0.75rem;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0 0 1rem 0;
  padding-left: 1rem;
`;

const NavItem = styled(motion.div)`
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.875rem 1rem;
  margin: 0.25rem 0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  
  &:hover {
    background: rgba(255,255,255,0.1);
    transform: translateX(4px);
  }
  
  &.active {
    background: linear-gradient(135deg, #f16925, #e05a1a);
    box-shadow: 0 4px 15px rgba(241, 105, 37, 0.3);
  }
  
  &.active::before {
    content: '';
    position: absolute;
    left: -1.5rem;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background: #f16925;
    border-radius: 0 4px 4px 0;
  }
`;

const NavIcon = styled.div`
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${props => props.active ? 'white' : '#94a3b8'};
`;

const NavText = styled.span`
  font-weight: 500;
  color: ${props => props.active ? 'white' : '#e2e8f0'};
`;

const AdminSidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const navItems = [
    { icon: <Calendar />, text: 'Events Management', path: '/admin/events' },
    { icon: <Users />, text: 'User Management', path: '/admin/users' },
    { icon: <Building2 />, text: 'Tenant Listings', path: '/admin/tenants' }
  ];

  const handleNavClick = (path) => {
    navigate(path);
  };

  return (
    <SidebarContainer
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <LogoSection>
        <Logo src="/logo.webp" alt="Mecrado Logo" />
        <LogoSubtitle>Admin Panel</LogoSubtitle>
      </LogoSection>

      <NavSection>
        <NavTitle>Main Navigation</NavTitle>
        {navItems.map((item, index) => {
          const isActive = location.pathname === item.path || 
                          (item.path !== '/admin' && location.pathname.startsWith(item.path));
          
          return (
            <NavItem
              key={index}
              className={isActive ? 'active' : ''}
              onClick={() => handleNavClick(item.path)}
              whileHover={{ x: 4 }}
              whileTap={{ scale: 0.98 }}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <NavIcon active={isActive}>
                {item.icon}
              </NavIcon>
              <NavText active={isActive}>{item.text}</NavText>
            </NavItem>
          );
        })}
      </NavSection>
    </SidebarContainer>
  );
};

export default AdminSidebar;
