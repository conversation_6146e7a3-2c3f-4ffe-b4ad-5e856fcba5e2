import React, { useState, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, Edit2, Trash2, Package, X, Image } from 'lucide-react';
import { useUserStore } from '../../store/useUserStore';
import { useAuthStore } from '../../store/useAuthStore';
import { useLanguageStore } from '../../store/useLanguageStore';
import { supabase } from '../../utils/supabaseClient';
import ImageUpload from '../../components/ImageUpload';
import Loader from '../../components/Loader';

const Container = styled(motion.div)`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  height: calc(100vh - 60px);
  overflow-y: auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  color: #2c3e50;
  margin: 0;
  font-size: 2rem;
  font-weight: 600;
`;

const Button = styled(motion.button)`
  background: #D8DF20;
  color: #000;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &:hover {
    background: #c4cb1c;
    transform: translateY(-1px);
  }
`;

const ProductsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  padding-bottom: 2rem;
`;

const ProductCard = styled(motion.div)`
  background: white;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.2s;
  display: flex;
  flex-direction: column;
  height: 100%;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
`;

const ProductImage = styled.div`
  width: 100%;
  height: 200px;
  background-color: #f8f9fa;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    padding: 1rem;
  }
`;

const ProductContent = styled.div`
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const ProductTitle = styled.h3`
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
`;

const ProductDescription = styled.p`
  color: #6c757d;
  margin: 0 0 1rem 0;
  font-size: 0.95rem;
  line-height: 1.5;
`;

const ProductPrice = styled.div`
  color: #2c3e50;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const EditButton = styled(Button)`
  background: #e9ecef;
  color: #495057;
  flex: 1;

  &:hover {
    background: #dee2e6;
  }
`;

const DeleteButton = styled(Button)`
  background: #ff4444;
  color: white;
  flex: 1;

  &:hover {
    background: #ff3333;
  }
`;

const Modal = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  z-index: 1000;
`;

const ModalContent = styled(motion.div)`
  background: white;
  padding: 2rem;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  position: relative;
  max-height: 90vh;
  overflow-y: auto;
`;

const ModalTitle = styled.h2`
  color: #2c3e50;
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding-right: 0.5rem;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  color: #495057;
  font-size: 0.9rem;
  font-weight: 500;
`;

const Input = styled.input`
  padding: 0.75rem;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  color: #212529;
  font-size: 0.95rem;
  transition: all 0.2s;

  &:focus {
    outline: none;
    border-color: #D8DF20;
    box-shadow: 0 0 0 2px rgba(216, 223, 32, 0.1);
  }
`;

const TextArea = styled.textarea`
  padding: 0.75rem;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  color: #212529;
  font-size: 0.95rem;
  min-height: 100px;
  resize: vertical;
  transition: all 0.2s;

  &:focus {
    outline: none;
    border-color: #D8DF20;
    box-shadow: 0 0 0 2px rgba(216, 223, 32, 0.1);
  }
`;

const CloseButton = styled.button`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s;

  &:hover {
    background: #f8f9fa;
    color: #495057;
  }
`;

const LoadingContainer = styled(motion.div)`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  color: #D8DF20;
  font-size: 1.2rem;
`;

const PostCategory = styled.span`
  display: inline-block;
  background: #D8DF20;
  color: #000;
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 1rem;
`;

const ImagePreview = styled.div`
  width: 100%;
  height: 200px;
  background-color: #f8f9fa;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-bottom: 1rem;

  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
`;

const ImageUploadSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
`;

const ImagePreviewContainer = styled.div`
  width: 100%;
  height: 200px;
  background-color: #f8f9fa;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border: 2px dashed #dee2e6;
  transition: all 0.2s;
  position: relative;

  &:hover {
    border-color: #D8DF20;
    background-color: #f1f3f5;
  }

  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
`;

const UploadButton = styled.button.attrs({ type: 'button' })`
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  background: #D8DF20;
  color: #000;
  padding: 0.75rem;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
  z-index: 2;

  &:hover {
    transform: scale(1.1);
    background: #c4cb1c;
  }
`;

const ImageUploadInput = styled.input`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
  z-index: 1;
`;

const UploadPlaceholder = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: #6c757d;
  text-align: center;
  padding: 1rem;
  pointer-events: none;

  svg {
    width: 48px;
    height: 48px;
  }
`;

const FormSection = styled.div`
  margin-bottom: 2rem;
`;

const SectionTitle = styled.h2`
  color: #2c3e50;
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  font-weight: 600;
`;

const TranslationNote = styled.p`
  color: #6c757d;
  margin: 0 0 1rem 0;
  font-size: 0.95rem;
  line-height: 1.5;
`;

const FormRow = styled.div`
  display: flex;
  gap: 1rem;
`;

const Select = styled.select`
  padding: 0.75rem;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  color: #212529;
  font-size: 0.95rem;
  transition: all 0.2s;

  &:focus {
    outline: none;
    border-color: #D8DF20;
    box-shadow: 0 0 0 2px rgba(216, 223, 32, 0.1);
  }
`;

const Products = () => {
  const { userData: _userData, standId } = useUserStore();
  const { user: _user } = useAuthStore();
  const { language } = useLanguageStore();
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [editingPost, setEditingPost] = useState(null);
  const [formData, setFormData] = useState({
    title_es: '',
    title_en: '',
    description_es: '',
    description_en: '',
    precio: '',
    category_es: '',
    category_en: '',
    image_url: ''
  });

  const fetchPosts = useCallback(async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('posts')
        .select('*')
        .eq('stand_id', standId);

      if (error) throw error;
      setPosts(data || []);
    } catch (error) {
      console.error('Error fetching posts:', error);
    } finally {
      setLoading(false);
    }
  }, [standId]);

  useEffect(() => {
    if (standId) {
      fetchPosts();
    }
  }, [standId, fetchPosts]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!standId) return;

    try {
      setLoading(true);
      
      // Prepare data for database
      const postData = {
        stand_id: standId,
        title_es: formData.title_es,
        title_en: formData.title_en,
        description_es: formData.description_es,
        description_en: formData.description_en,
        precio: formData.precio,
        category_es: formData.category_es,
        category_en: formData.category_en,
        image_url: formData.image_url,
        // Keep legacy fields for backward compatibility
        title: formData.title_es || formData.title_en,
        description: formData.description_es || formData.description_en,
        category: formData.category_es || formData.category_en
      };

      if (editingPost) {
        const { error } = await supabase
          .from('posts')
          .update(postData)
          .eq('id', editingPost.id);
        if (error) throw error;
      } else {
        const { error } = await supabase
          .from('posts')
          .insert([postData]);
        if (error) throw error;
      }

      fetchPosts();
      setShowModal(false);
      resetForm();
    } catch (error) {
      console.error('Error saving post:', error);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      title_es: '',
      title_en: '',
      description_es: '',
      description_en: '',
      precio: '',
      category_es: '',
      category_en: '',
      image_url: ''
    });
    setEditingPost(null);
  };

  const handleEditPost = (post) => {
    setEditingPost(post);
    setFormData({
      title_es: post.title_es || post.title || '',
      title_en: post.title_en || '',
      description_es: post.description_es || post.description || '',
      description_en: post.description_en || '',
      precio: post.precio || '',
      category_es: post.category_es || post.category || '',
      category_en: post.category_en || '',
      image_url: post.image_url || ''
    });
    setShowModal(true);
  };

  const handleDeletePost = async (postId) => {
    const confirmMessage = language === 'es' 
      ? '¿Estás seguro de que deseas eliminar este post?'
      : 'Are you sure you want to delete this post?';
    
    if (!confirm(confirmMessage)) return;

    try {
      setLoading(true);
      const { error } = await supabase
        .from('posts')
        .delete()
        .eq('id', postId);

      if (error) throw error;
      fetchPosts();
    } catch (error) {
      console.error('Error al eliminar el post:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddPost = () => {
    resetForm();
    setShowModal(true);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Helper function to delete old product image from storage
  const deleteOldProductImage = async (imageUrl) => {
    try {
      if (!imageUrl || !imageUrl.includes('supabase')) {
        console.log('No valid product image URL to delete:', imageUrl);
        return;
      }

      console.log('Attempting to delete old product image:', imageUrl);

      // Extract file path from Supabase storage URL
      // URL format: https://project.supabase.co/storage/v1/object/public/images/user-id/products/filename
      const urlParts = imageUrl.split('/');
      const bucketIndex = urlParts.findIndex(part => part === 'images');

      if (bucketIndex === -1 || bucketIndex >= urlParts.length - 1) {
        console.warn('Could not extract file path from product URL:', imageUrl);
        return;
      }

      // Get everything after 'images/' as the file path
      const filePath = urlParts.slice(bucketIndex + 1).join('/');
      console.log('Extracted product file path for deletion:', filePath);

      const { error } = await supabase.storage
        .from('images')
        .remove([filePath]);

      if (error) {
        console.error('Supabase product deletion error:', error);
        console.error('Failed to delete product file path:', filePath);
      } else {
        console.log('✅ Old product image deleted successfully:', filePath);
      }
    } catch (error) {
      console.error('Error in deleteOldProductImage function:', error);
    }
  };

  const handleFileChange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    try {
      setLoading(true);

      // Import compression utilities
      const { compressImage, getCompressionSettings, validateImageFile, formatFileSize } = await import('../../utils/imageCompression');

      // Validate and compress the image
      validateImageFile(file);
      console.log(`Original product image size: ${formatFileSize(file.size)}`);

      // Get compression settings and preserve original format
      const compressionSettings = getCompressionSettings('product', file.type);
      console.log(`Original format: ${file.type}, Target format: ${compressionSettings.format}`);

      const compressedFile = await compressImage(file, compressionSettings);
      console.log(`Compressed product image size: ${formatFileSize(compressedFile.size)}`);

      // Generate file path for compressed image FIRST
      const fileExt = compressedFile.name.split('.').pop();
      const userIdFolder = _user?.id || 'general';
      const filePath = `${userIdFolder}/products/${Date.now()}.${fileExt}`;

      // Get public URL BEFORE upload (no egress charge)
      const { data: { publicUrl } } = supabase.storage
        .from('images')
        .getPublicUrl(filePath);

      // Delete old image FIRST to prevent double egress
      if (editingPost && editingPost.image_url) {
        console.log('🗑️ Deleting old product image BEFORE upload to prevent double egress');
        await deleteOldProductImage(editingPost.image_url);
      }

      // Upload compressed image AFTER deletion
      const { data: _data, error: uploadError } = await supabase.storage
        .from('images')
        .upload(filePath, compressedFile, {
          cacheControl: '31536000', // 1 year cache
          upsert: true,
          contentType: compressedFile.type
        });

      if (uploadError) {
        console.error('Upload error details:', uploadError);
        throw uploadError;
      }

      // 1) Update local form state with egress optimization
      setFormData(prev => ({
        ...prev,
        image_url: publicUrl
      }));

      // Add small delay to prevent immediate image loading that causes egress
      console.log('⏳ Waiting 500ms before UI update to optimize egress usage');
      await new Promise(resolve => setTimeout(resolve, 500));

      // 2) If we are currently editing an existing post, immediately persist the new image URL
      if (editingPost) {
        const { error: updateError } = await supabase
          .from('posts')
          .update({ image_url: publicUrl })
          .eq('id', editingPost.id);

        if (updateError) {
          console.error('Error updating post image_url:', updateError);
        } else {
          console.log('Post image_url updated successfully');
          // Update local editingPost to reflect new image URL so Save button also has it
          setEditingPost(prev => ({ ...prev, image_url: publicUrl }));
          // Refresh list so the UI shows the new image
          fetchPosts();
        }
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Error al subir la imagen: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleImageClick = (e) => {
    // Find the nearest file input within the same preview container to avoid accidentally selecting another hidden input
    const wrapper = e.currentTarget.closest('div');
    const fileInput = wrapper ? wrapper.querySelector('input[type="file"]') : null;
    (fileInput || document.querySelector('input[type="file"]'))?.click();
  };

  // Helper function to get the appropriate text based on current language
  const getLocalizedText = (item, field) => {
    const esField = `${field}_es`;
    const enField = `${field}_en`;
    const fallbackField = field;

    if (language === 'es') {
      return item[esField] || item[enField] || item[fallbackField] || '';
    } else {
      return item[enField] || item[esField] || item[fallbackField] || '';
    }
  };

  if (loading) {
    return <Loader />;
  }

  return (
    <Container
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Header>
        <Title>{language === 'es' ? 'Productos y Servicios' : 'Products and Services'}</Title>
        <Button
          onClick={handleAddPost}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Plus size={20} />
          {language === 'es' ? 'Agregar Producto' : 'Add Product'}
        </Button>
      </Header>

      <ProductsGrid>
        {posts.map((post, index) => (
          <ProductCard
            key={post.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2, delay: index * 0.1 }}
            whileHover={{ scale: 1.02 }}
          >
            <ProductImage>
              {post.image_url ? (
                <img 
                  src={post.image_url} 
                  alt={getLocalizedText(post, 'title')} 
                  loading={index < 8 ? 'eager' : 'lazy'}
                  fetchpriority={index < 8 ? 'high' : 'auto'}
                />
              ) : (
                <Package size={48} style={{ color: '#6c757d' }} />
              )}
            </ProductImage>
            <ProductContent>
              <ProductTitle>{getLocalizedText(post, 'title')}</ProductTitle>
              {getLocalizedText(post, 'category') && (
                <PostCategory>{getLocalizedText(post, 'category')}</PostCategory>
              )}
              <ProductDescription>{getLocalizedText(post, 'description')}</ProductDescription>
              <ProductPrice>${post.precio}</ProductPrice>
              <ButtonGroup>
                <EditButton
                  onClick={() => handleEditPost(post)}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <Edit2 size={16} />
                </EditButton>
                <DeleteButton
                  onClick={() => handleDeletePost(post.id)}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <Trash2 size={16} />
                </DeleteButton>
              </ButtonGroup>
            </ProductContent>
          </ProductCard>
        ))}
      </ProductsGrid>

      <AnimatePresence>
        {showModal && (
          <Modal
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowModal(false)}
          >
            <ModalContent
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={e => e.stopPropagation()}
            >
              <CloseButton onClick={() => setShowModal(false)}>
                <X size={20} />
              </CloseButton>
              <ModalTitle>
                {editingPost 
                  ? (language === 'es' ? 'Editar Producto' : 'Edit Product')
                  : (language === 'es' ? 'Nuevo Producto' : 'New Product')
                }
              </ModalTitle>
              <Form onSubmit={handleSubmit}>
                <ImageUploadSection>
                  <Label>{language === 'es' ? 'Imagen del Producto' : 'Product Image'}</Label>
                  <ImagePreviewContainer>
                    {formData.image_url ? (
                      <>
                        <img src={formData.image_url} alt={language === 'es' ? 'Vista previa' : 'Preview'} loading="lazy" />
                        <UploadButton onClick={handleImageClick}>
                          <Image size={24} />
                        </UploadButton>
                      </>
                    ) : (
                      <UploadPlaceholder>
                        <Image size={48} />
                        <span>{language === 'es' ? 'Haz clic en el botón de cámara' : 'Click the camera button'}</span>
                        <span>{language === 'es' ? 'para subir una imagen' : 'to upload an image'}</span>
                      </UploadPlaceholder>
                    )}
                    <ImageUploadInput 
                      type="file" 
                      accept="image/*" 
                      onChange={handleFileChange}
                    />
                  </ImagePreviewContainer>
                </ImageUploadSection>

                <FormSection>
                  <SectionTitle>
                    {language === 'es' ? 'Información del Producto/Servicio' : 'Product/Service Information'}
                  </SectionTitle>
                  
                  {language === 'es' ? (
                    <TranslationNote>
                      💡 <strong>Consejo:</strong> Completa la información en ambos idiomas (español e inglés) para aumentar la visibilidad de tus productos.
                    </TranslationNote>
                  ) : (
                    <TranslationNote>
                      💡 <strong>Tip:</strong> Fill out information in both languages (Spanish and English) to increase your product visibility.
                    </TranslationNote>
                  )}

                  <FormRow>
                    <FormGroup>
                      <Label>{language === 'es' ? 'Título (Español)' : 'Title (Spanish)'} *</Label>
                      <Input
                        type="text"
                        value={formData.title_es}
                        onChange={(e) => setFormData({...formData, title_es: e.target.value})}
                        placeholder={language === 'es' ? 'Ej: Hamburguesa Especial' : 'Ex: Hamburguesa Especial'}
                        required
                      />
                    </FormGroup>
                    <FormGroup>
                      <Label>{language === 'es' ? 'Título (Inglés)' : 'Title (English)'}</Label>
                      <Input
                        type="text"
                        value={formData.title_en}
                        onChange={(e) => setFormData({...formData, title_en: e.target.value})}
                        placeholder={language === 'es' ? 'Ej: Special Burger' : 'Ex: Special Burger'}
                      />
                    </FormGroup>
                  </FormRow>

                  <FormRow>
                    <FormGroup>
                      <Label>{language === 'es' ? 'Descripción (Español)' : 'Description (Spanish)'} *</Label>
                      <TextArea
                        value={formData.description_es}
                        onChange={(e) => setFormData({...formData, description_es: e.target.value})}
                        placeholder={language === 'es' ? 'Describe tu producto o servicio en español...' : 'Describe your product or service in Spanish...'}
                        rows="4"
                        required
                      />
                    </FormGroup>
                    <FormGroup>
                      <Label>{language === 'es' ? 'Descripción (Inglés)' : 'Description (English)'}</Label>
                      <TextArea
                        value={formData.description_en}
                        onChange={(e) => setFormData({...formData, description_en: e.target.value})}
                        placeholder={language === 'es' ? 'Describe tu producto o servicio en inglés...' : 'Describe your product or service in English...'}
                        rows="4"
                      />
                    </FormGroup>
                  </FormRow>

                  <FormRow>
                    <FormGroup>
                      <Label>{language === 'es' ? 'Categoría (Español)' : 'Category (Spanish)'} *</Label>
                      <Select
                        value={formData.category_es}
                        onChange={(e) => setFormData({...formData, category_es: e.target.value})}
                        required
                      >
                        <option value="">{language === 'es' ? 'Seleccionar categoría...' : 'Select category...'}</option>
                        <option value="producto">Producto</option>
                        <option value="servicio">Servicio</option>
                        <option value="comida">Comida</option>
                        <option value="bebida">Bebida</option>
                        <option value="ropa">Ropa</option>
                        <option value="accesorios">Accesorios</option>
                        <option value="electrónicos">Electrónicos</option>
                        <option value="hogar">Hogar</option>
                        <option value="salud">Salud y Belleza</option>
                        <option value="otro">Otro</option>
                      </Select>
                    </FormGroup>
                    <FormGroup>
                      <Label>{language === 'es' ? 'Categoría (Inglés)' : 'Category (English)'}</Label>
                      <Select
                        value={formData.category_en}
                        onChange={(e) => setFormData({...formData, category_en: e.target.value})}
                      >
                        <option value="">{language === 'es' ? 'Seleccionar categoría...' : 'Select category...'}</option>
                        <option value="product">Product</option>
                        <option value="service">Service</option>
                        <option value="food">Food</option>
                        <option value="drink">Drink</option>
                        <option value="clothing">Clothing</option>
                        <option value="accessories">Accessories</option>
                        <option value="electronics">Electronics</option>
                        <option value="home">Home</option>
                        <option value="health">Health & Beauty</option>
                        <option value="other">Other</option>
                      </Select>
                    </FormGroup>
                  </FormRow>
                </FormSection>

                <FormGroup>
                  <Label>{language === 'es' ? 'Precio' : 'Price'}</Label>
                  <Input
                    type="text"
                    name="precio"
                    value={formData.precio || ''}
                    onChange={handleChange}
                    placeholder={language === 'es' ? '0.00' : '0.00'}
                    required
                  />
                </FormGroup>

                <ButtonGroup>
                  <Button
                    type="submit"
                    disabled={loading}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {loading 
                      ? (language === 'es' ? 'Guardando...' : 'Saving...') 
                      : (language === 'es' ? 'Guardar' : 'Save')
                    }
                  </Button>
                  <Button
                    type="button"
                    onClick={() => setShowModal(false)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {language === 'es' ? 'Cancelar' : 'Cancel'}
                  </Button>
                </ButtonGroup>
              </Form>
            </ModalContent>
          </Modal>
        )}
      </AnimatePresence>
    </Container>
  );
};

export default Products; 