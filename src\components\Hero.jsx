import React, { useRef, useEffect } from 'react';
import styled from 'styled-components';
import { Link, useNavigate } from 'react-router-dom';
import { useLanguageStore } from '../store/useLanguageStore';

const HeroContainer = styled.section`
  position: relative;
  width: 100%;
  height: 75vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding: 0 3rem;
  @media (max-width: 900px) {
    padding: 0 2rem;
  }
  @media (max-width: 600px) {
    padding: 0 1rem;
  }
`;

const VideoBackground = styled.video`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
`;

const Overlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  z-index: 2;
`;

const CenteredTitle = styled.h1`
  position: relative;
  z-index: 3;
  color: #fff;
  font-family: '<PERSON>bas Neue', sans-serif;
  font-size: 8rem;
  text-align: center;
  letter-spacing: 2px;
  text-shadow: 2px 2px 8px rgba(0,0,0,0.4);
  margin: 0;
  /* Always visible */
  opacity: 1;
  transform: translateY(0);
  transition: none;
  @media (max-width: 768px) {
    font-size: 4rem;
  }
`;

const MarketsButton = styled.button`
  margin-top: 2.5rem;
  background: transparent;
  color: #fff;
  border: 3px solid #fff;
  border-radius: 10px;
  padding: 1rem 3rem;
  font-size: 2rem;
  font-family: 'Bebas Neue', sans-serif;
  font-weight: 700;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.8s ease-out;
  z-index: 3;
  /* Always visible */
  opacity: 1;
  transform: translateY(0);
  &:hover {
    background: rgba(255,255,255,0.08);
    color: #fff;
    border-color: #fff;
  }
`;

const Hero = () => {
  const { language } = useLanguageStore();
  const navigate = useNavigate();
  const videoRef = useRef(null);

  const handleTicketsClick = () => {
    window.open('https://www.ticketleap.events/tickets/mercadofoodhall/foodiefest-2025', '_blank');
  };

  useEffect(() => {
    const video = videoRef.current;
    if (video) {
      // Ensure video loops properly
      video.addEventListener('ended', () => {
        video.currentTime = 0;
        video.play();
      });
    }
  }, []);

  return (
    <HeroContainer>
      <VideoBackground
        ref={videoRef}
        autoPlay
        loop
        muted
        playsInline
        preload="auto"
        poster="/banner-mall.webp"
      >
        <source src="/video.mp4" type="video/mp4" />
        {/* Fallback text for accessibility */}
        Your browser does not support the video tag.
      </VideoBackground>
      <Overlay />
      <CenteredTitle>Mercado Foodie Fest</CenteredTitle>
      <MarketsButton onClick={handleTicketsClick}>
        Tickets On Sale
      </MarketsButton>
    </HeroContainer>
  );
};

export default Hero;
