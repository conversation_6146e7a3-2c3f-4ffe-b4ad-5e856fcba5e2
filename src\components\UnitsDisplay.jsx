import React from 'react';
import styled from 'styled-components';
import { Building2 } from 'lucide-react';

const UnitsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
`;

const UnitBadge = styled.span`
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: ${props => props.$variant === 'primary' ? '#F16925' : '#e9ecef'};
  color: ${props => props.$variant === 'primary' ? 'white' : '#495057'};
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
`;

const UnitsText = styled.span`
  color: #495057;
  font-size: 0.875rem;
  font-weight: 500;
`;

const UnitsLabel = styled.span`
  color: #6c757d;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-right: 0.25rem;
`;

const IconWrapper = styled.span`
  display: inline-flex;
  align-items: center;
  color: ${props => props.$variant === 'primary' ? 'white' : '#6c757d'};
`;

/**
 * UnitsDisplay Component
 * 
 * Displays unit numbers in a consistent, visually appealing format
 * Handles both single and multiple units gracefully
 * 
 * @param {Object} props
 * @param {Array|string} props.units - Array of unit numbers or single unit string
 * @param {string} props.fallbackUnit - Fallback single unit value (legacy support)
 * @param {string} props.variant - Display variant: 'badges', 'text', 'compact'
 * @param {boolean} props.showIcon - Whether to show building icon
 * @param {boolean} props.showLabel - Whether to show "Units:" label
 * @param {string} props.language - Language for labels ('en' or 'es')
 */
const UnitsDisplay = ({
  units,
  fallbackUnit,
  variant = 'badges',
  showIcon = false,
  showLabel = false,
  language = 'en'
}) => {
  // Determine the units to display
  let displayUnits = [];

  // Helper to normalise any string into an array of trimmed unit identifiers
  const normaliseUnitsString = (str) => {
    if (!str) return [];

    // Remove potential Postgres array braces e.g., "{B-113,B-114}"
    const cleaned = str.replace(/^\{/, '').replace(/\}$/,'');

    const delimitersRegex = /[\s,/&\n\r]+/; // space, comma, slash, ampersand, new line

    return cleaned
      .split(delimitersRegex)
      .map(u => u.trim())
      .filter(u => u !== '');
  };

  if (Array.isArray(units) && units.length > 0) {
    // Use the units array if it exists and has content
    displayUnits = units.filter(unit => unit && unit.toString().trim() !== '');
  } else if (typeof units === 'string' && units.trim() !== '') {
    // Handle case where units comes back as a Postgres array literal or comma-separated string
    displayUnits = normaliseUnitsString(units);
  }

  // If still empty, try fallbackUnit legacy field
  if (displayUnits.length === 0 && fallbackUnit && fallbackUnit.toString().trim() !== '') {
    // Handle a variety of delimiters: comma, slash, ampersand, whitespace and line breaks
    const unitStr = fallbackUnit.toString().trim();

    const delimitersRegex = /[\s,/&\n\r]+/;

    // Split using the regex, trim and filter empty strings
    let parsedUnits = unitStr
      .split(delimitersRegex)
      .map(u => u.trim())
      .filter(u => u !== '');

    // If, for some reason, the regex did not split (e.g., exotic separator), fall back to the original string
    if (parsedUnits.length === 0) {
      parsedUnits = [unitStr];
    }

    // Deduplicate while preserving order (e.g., "B-113 B-113" → "B-113")
    displayUnits = [...new Set(parsedUnits)];
  }

  // If no units to display, return null or a placeholder
  if (displayUnits.length === 0) {
    return variant === 'text' ? (
      <UnitsText>{language === 'es' ? 'N/D' : 'N/A'}</UnitsText>
    ) : null;
  }

  // Get labels based on language
  const labels = {
    unit: language === 'es' ? 'Unidad' : 'Unit',
    units: language === 'es' ? 'Unidades' : 'Units'
  };

  const isMultiple = displayUnits.length > 1;
  const label = isMultiple ? labels.units : labels.unit;

  // Render based on variant
  switch (variant) {
    case 'badges':
      return (
        <UnitsContainer>
          {showLabel && <UnitsLabel>{label}:</UnitsLabel>}
          {displayUnits.map((unit, index) => (
            <UnitBadge key={index} $variant={index === 0 ? 'primary' : 'secondary'}>
              {showIcon && index === 0 && (
                <IconWrapper $variant={index === 0 ? 'primary' : 'secondary'}>
                  <Building2 size={14} />
                </IconWrapper>
              )}
              {unit}
            </UnitBadge>
          ))}
        </UnitsContainer>
      );

    case 'text':
      return (
        <UnitsContainer>
          {showIcon && (
            <IconWrapper>
              <Building2 size={16} />
            </IconWrapper>
          )}
          {showLabel && <UnitsLabel>{label}:</UnitsLabel>}
          <UnitsText>{displayUnits.join(', ')}</UnitsText>
        </UnitsContainer>
      );

    case 'compact':
      return (
        <UnitsContainer>
          {showIcon && (
            <IconWrapper>
              <Building2 size={14} />
            </IconWrapper>
          )}
          <UnitsText>
            {isMultiple ? `${label} ` : ''}
            {displayUnits.join(', ')}
          </UnitsText>
        </UnitsContainer>
      );

    default:
      return (
        <UnitsContainer>
          <UnitsText>{displayUnits.join(', ')}</UnitsText>
        </UnitsContainer>
      );
  }
};

export default UnitsDisplay;
