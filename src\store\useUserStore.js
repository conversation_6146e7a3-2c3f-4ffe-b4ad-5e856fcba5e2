import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { supabase } from '../utils/supabaseClient';

export const useUserStore = create(persist(
  (set, get) => ({
    userData: null,
    role: null,
    standId: null,
    standData: null,
    loading: false,
    error: null,
    isAdmin: false,

    fetchUserData: async (userId) => {
      try {
        set({ loading: true, error: null });
        
        const { data, error } = await supabase
          .from('users')
          .select('*')
          .eq('id', userId)
          .single();

        if (error) throw error;

        // Fetch stand separately to ensure we get the correct one
        const { data: standData, error: standError } = await supabase
          .from('stands')
          .select('*')
          .eq('user_id', userId)
          .maybeSingle();

        if (standError && standError.code !== 'PGRST116') {
          console.error('Error fetching stand:', standError);
        }

        // Check if user is admin
        const isAdmin = data.role === 'admin' && data.is_admin === true;

        set({
          userData: data,
          role: data.role,
          standId: standData?.id || null,
          standData: standData || null,
          isAdmin,
          loading: false
        });

        return data;
      } catch (error) {
        console.error('Error fetching user data:', error);
        set({ error: error.message, loading: false });
        throw error;
      }
    },

    setUserData: (userData) => {
      const isAdmin = userData?.role === 'admin' && userData?.is_admin === true;
      set({ 
        userData, 
        role: userData?.role || null,
        isAdmin 
      });
    },

    setStandData: (standData) => {
      set({ 
        standData, 
        standId: standData?.id || null 
      });
    },

    clearUserData: () => {
      set({
        userData: null,
        role: null,
        standId: null,
        standData: null,
        isAdmin: false,
        error: null
      });
    },

    signOut: () => {
      get().clearUserData();
    },

    setLoading: (loading) => set({ loading }),
    setError: (error) => set({ error })
  }),
  {
    name: 'user-storage',
    partialize: (state) => ({
      userData: state.userData,
      role: state.role,
      standId: state.standId,
      standData: state.standData,
      isAdmin: state.isAdmin
    })
  }
)); 