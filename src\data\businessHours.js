// Fake API para horarios de negocio
export const businessHoursData = {
  // Horarios por categoría por defecto
  categories: {
    Restaurant: {
      monday: { isOpen: false },
      tuesday: { isOpen: true, open: "11:00", close: "22:00" },
      wednesday: { isOpen: true, open: "11:00", close: "22:00" },
      thursday: { isOpen: true, open: "11:00", close: "22:00" },
      friday: { isOpen: true, open: "11:00", close: "23:00" },
      saturday: { isOpen: true, open: "11:00", close: "23:00" },
      sunday: { isOpen: true, open: "11:00", close: "18:00" }
    },
    Retail: {
      monday: { isOpen: false },
      tuesday: { isOpen: true, open: "12:00", close: "20:00" },
      wednesday: { isOpen: true, open: "12:00", close: "20:00" },
      thursday: { isOpen: true, open: "12:00", close: "20:00" },
      friday: { isOpen: true, open: "12:00", close: "20:00" },
      saturday: { isOpen: true, open: "11:00", close: "20:00" },
      sunday: { isOpen: true, open: "11:00", close: "18:00" }
    },
    Beauty: {
      monday: { isOpen: false },
      tuesday: { isOpen: true, open: "10:00", close: "19:00" },
      wednesday: { isOpen: true, open: "10:00", close: "19:00" },
      thursday: { isOpen: true, open: "10:00", close: "19:00" },
      friday: { isOpen: true, open: "10:00", close: "20:00" },
      saturday: { isOpen: true, open: "10:00", close: "20:00" },
      sunday: { isOpen: true, open: "11:00", close: "17:00" }
    }
  },

  // Horarios específicos para establecimientos particulares
  specificStores: {
    "Blu Brasserie": {
      monday: { isOpen: false },
      tuesday: { isOpen: true, open: "17:00", close: "22:00" },
      wednesday: { isOpen: true, open: "17:00", close: "22:00" },
      thursday: { isOpen: true, open: "17:00", close: "22:00" },
      friday: { isOpen: true, open: "17:00", close: "22:00" },
      saturday: { isOpen: true, open: "17:00", close: "22:00" },
      sunday: { isOpen: false } // Blu Brasserie solo martes-sábado
    },
    "Doña Lula": {
      monday: { isOpen: false },
      tuesday: { isOpen: true, open: "12:00", close: "21:00" },
      wednesday: { isOpen: true, open: "12:00", close: "21:00" },
      thursday: { isOpen: true, open: "12:00", close: "21:00" },
      friday: { isOpen: true, open: "12:00", close: "22:00" },
      saturday: { isOpen: true, open: "12:00", close: "22:00" },
      sunday: { isOpen: true, open: "12:00", close: "18:00" }
    },
    "Inka Wasi": {
      monday: { isOpen: false },
      tuesday: { isOpen: true, open: "11:30", close: "21:30" },
      wednesday: { isOpen: true, open: "11:30", close: "21:30" },
      thursday: { isOpen: true, open: "11:30", close: "21:30" },
      friday: { isOpen: true, open: "11:30", close: "22:30" },
      saturday: { isOpen: true, open: "11:30", close: "22:30" },
      sunday: { isOpen: true, open: "11:30", close: "18:00" }
    }
  }
};

// Obtener día de la semana en inglés
const getDayOfWeek = () => {
  const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
  return days[new Date().getDay()];
};

// Obtener hora actual en formato HH:MM
const getCurrentTime = () => {
  const now = new Date();
  return now.toTimeString().slice(0, 5); // "HH:MM"
};

// Comparar si una hora está entre dos horarios
const isTimeBetween = (currentTime, openTime, closeTime) => {
  const current = currentTime.replace(':', '');
  const open = openTime.replace(':', '');
  const close = closeTime.replace(':', '');
  
  return current >= open && current <= close;
};

// API Principal: Obtener horarios de un establecimiento
export const getBusinessHours = (storeName, category) => {
  // Verificar si hay horarios específicos para este establecimiento
  if (businessHoursData.specificStores[storeName]) {
    return businessHoursData.specificStores[storeName];
  }
  
  // Si no, usar horarios por categoría
  if (businessHoursData.categories[category]) {
    return businessHoursData.categories[category];
  }
  
  // Horarios por defecto si no se encuentra la categoría
  return businessHoursData.categories.Restaurant;
};

// API: Verificar si un establecimiento está abierto ahora
export const isStoreOpenNow = (storeName, category) => {
  const hours = getBusinessHours(storeName, category);
  const today = getDayOfWeek();
  const currentTime = getCurrentTime();
  
  const todayHours = hours[today];
  
  if (!todayHours || !todayHours.isOpen) {
    return false;
  }
  
  return isTimeBetween(currentTime, todayHours.open, todayHours.close);
};

// API: Obtener estado actual del establecimiento
export const getStoreStatus = (storeName, category, language = 'es') => {
  const isOpen = isStoreOpenNow(storeName, category);
  const hours = getBusinessHours(storeName, category);
  const today = getDayOfWeek();
  const todayHours = hours[today];
  
  // Casos especiales
  if (storeName === 'Blu Brasserie') {
    if (!todayHours || !todayHours.isOpen) {
      return {
        status: language === 'es' ? 'Próximamente' : 'Coming Soon',
        isOpen: false,
        color: '#F16925',
        backgroundColor: 'rgba(241, 105, 37, 0.1)'
      };
    }
  }
  
  if (!todayHours || !todayHours.isOpen) {
    return {
      status: language === 'es' ? 'Cerrado' : 'Closed',
      isOpen: false,
      color: '#dc3545',
      backgroundColor: 'rgba(220, 53, 69, 0.1)'
    };
  }
  
  if (isOpen) {
    return {
      status: language === 'es' ? 'Abierto Ahora' : 'Open Now',
      isOpen: true,
      color: '#28a745',
      backgroundColor: 'rgba(40, 167, 69, 0.1)'
    };
  } else {
    return {
      status: language === 'es' ? 'Cerrado' : 'Closed',
      isOpen: false,
      color: '#dc3545',
      backgroundColor: 'rgba(220, 53, 69, 0.1)'
    };
  }
};

// Formatear hora de 24h a 12h (AM/PM)
const formatTimeToAMPM = (time24) => {
  if (!time24) return '';
  
  const [hours, minutes] = time24.split(':');
  const hour12 = parseInt(hours);
  const ampm = hour12 >= 12 ? 'PM' : 'AM';
  const displayHour = hour12 % 12 || 12;
  
  return `${displayHour}:${minutes} ${ampm}`;
};

// API: Obtener horarios formateados para mostrar en UI
export const getFormattedHours = (storeName, category, language = 'es') => {
  const hours = getBusinessHours(storeName, category);
  
  const dayNames = {
    es: {
      monday: 'Lunes',
      tuesday: 'Martes',
      wednesday: 'Miércoles',
      thursday: 'Jueves',
      friday: 'Viernes',
      saturday: 'Sábado',
      sunday: 'Domingo'
    },
    en: {
      monday: 'Monday',
      tuesday: 'Tuesday',
      wednesday: 'Wednesday',
      thursday: 'Thursday',
      friday: 'Friday',
      saturday: 'Saturday',
      sunday: 'Sunday'
    }
  };
  
  const closedText = language === 'es' ? 'Cerrado' : 'Closed';
  
  return Object.entries(hours).map(([day, schedule]) => ({
    day: dayNames[language][day],
    hours: schedule.isOpen 
      ? `${formatTimeToAMPM(schedule.open)} - ${formatTimeToAMPM(schedule.close)}`
      : closedText,
    isOpen: schedule.isOpen
  }));
};

// API: Obtener próximo horario de apertura
export const getNextOpenTime = (storeName, category, language = 'es') => {
  const hours = getBusinessHours(storeName, category);
  const currentDay = new Date().getDay();
  
  // Buscar próximo día abierto
  for (let i = 1; i <= 7; i++) {
    const nextDayIndex = (currentDay + i) % 7;
    const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    const nextDay = dayNames[nextDayIndex];
    
    if (hours[nextDay] && hours[nextDay].isOpen) {
      const dayNamesTranslated = {
        es: ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'],
        en: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
      };
      
      return {
        day: dayNamesTranslated[language][nextDayIndex],
        time: hours[nextDay].open
      };
    }
  }
  
  return null;
}; 