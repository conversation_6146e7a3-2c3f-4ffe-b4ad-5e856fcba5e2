// Utilidades adicionales para horarios de negocio

// Formatear hora de 24h a 12h (AM/PM)
export const formatTimeToAMPM = (time24) => {
  if (!time24) return '';
  
  const [hours, minutes] = time24.split(':');
  const hour12 = parseInt(hours);
  const ampm = hour12 >= 12 ? 'PM' : 'AM';
  const displayHour = hour12 % 12 || 12;
  
  return `${displayHour}:${minutes} ${ampm}`;
};

// Simular diferentes horas para testing
export const simulateTime = (hour, minute = 0) => {
  const testDate = new Date();
  testDate.setHours(hour, minute, 0, 0);
  return testDate;
};

// Simular diferentes días para testing
export const simulateDay = (dayIndex) => {
  // 0 = Sunday, 1 = Monday, etc.
  const testDate = new Date();
  const currentDay = testDate.getDay();
  const diff = dayIndex - currentDay;
  testDate.setDate(testDate.getDate() + diff);
  return testDate;
};

// Debug: Mostrar información de horarios para un establecimiento
export const debugStoreHours = (storeName, category) => {
  import('../data/businessHours').then(({ getBusinessHours, isStoreOpenNow, getStoreStatus }) => {
    console.group(`🏪 Debug: ${storeName} (${category})`);
    
    const hours = getBusinessHours(storeName, category);
    const isOpen = isStoreOpenNow(storeName, category);
    const status = getStoreStatus(storeName, category);
    
    console.log('📅 Horarios:', hours);
    console.log('🕐 Abierto ahora:', isOpen);
    console.log('📊 Estado:', status);
    
    // Mostrar horarios formateados
    const days = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
    const dayKeys = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    
    console.table(
      dayKeys.map((dayKey, index) => ({
        Día: days[index],
        Abierto: hours[dayKey]?.isOpen ? 'Sí' : 'No',
        Horario: hours[dayKey]?.isOpen 
          ? `${formatTimeToAMPM(hours[dayKey].open)} - ${formatTimeToAMPM(hours[dayKey].close)}`
          : 'Cerrado'
      }))
    );
    
    console.groupEnd();
  });
};

// Testear horarios en diferentes momentos del día
export const testStoreAtDifferentTimes = (storeName, category) => {
  import('../data/businessHours').then(({ isStoreOpenNow, getStoreStatus }) => {
    console.group(`🕐 Test horarios: ${storeName}`);
    
    const testTimes = [
      { label: '8:00 AM', hour: 8 },
      { label: '10:00 AM', hour: 10 },
      { label: '12:00 PM', hour: 12 },
      { label: '3:00 PM', hour: 15 },
      { label: '6:00 PM', hour: 18 },
      { label: '9:00 PM', hour: 21 },
      { label: '11:00 PM', hour: 23 }
    ];
    
    // Guardar fecha actual
    const originalDate = Date;
    
    testTimes.forEach(({ label, hour }) => {
      // Simular hora específica
      const mockDate = simulateTime(hour);
      global.Date = class extends Date {
        constructor(...args) {
          if (args.length === 0) {
            return mockDate;
          }
          return new originalDate(...args);
        }
        static now() {
          return mockDate.getTime();
        }
      };
      
      const status = getStoreStatus(storeName, category);
      console.log(`${label}: ${status.status}`);
    });
    
    // Restaurar fecha original
    global.Date = originalDate;
    
    console.groupEnd();
  });
};

// Obtener resumen de todos los establecimientos
export const getAllStoresStatus = () => {
  import('../data/businessHours').then(({ businessHoursData, getStoreStatus }) => {
    console.group('🏬 Estado de todos los establecimientos');
    
    // Establecimientos específicos
    Object.keys(businessHoursData.specificStores).forEach(storeName => {
      const status = getStoreStatus(storeName, 'Restaurant'); // Asumimos Restaurant por defecto
      console.log(`${storeName}: ${status.status}`);
    });
    
    console.groupEnd();
  });
};

// Función para agregar fácilmente nuevos establecimientos
export const addNewStore = (storeName, category, hours) => {
  console.warn('🚧 Esta función es solo para desarrollo. Para agregar establecimientos en producción, modifica businessHours.js');
  console.log('Formato de horarios sugerido:', {
    monday: { isOpen: false },
    tuesday: { isOpen: true, open: "12:00", close: "20:00" },
    // ... resto de días
  });
  
  return {
    name: storeName,
    category,
    suggestedHours: hours,
    instructions: 'Agregar manualmente a businessHoursData.specificStores en businessHours.js'
  };
};

// Validar formato de horarios
export const validateHoursFormat = (hours) => {
  const dayKeys = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  const errors = [];
  
  dayKeys.forEach(day => {
    if (!hours[day]) {
      errors.push(`Falta el día: ${day}`);
      return;
    }
    
    const daySchedule = hours[day];
    
    if (typeof daySchedule.isOpen !== 'boolean') {
      errors.push(`${day}: isOpen debe ser boolean`);
    }
    
    if (daySchedule.isOpen) {
      if (!daySchedule.open || !daySchedule.close) {
        errors.push(`${day}: Falta hora de apertura o cierre`);
      }
      
      // Validar formato HH:MM
      const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
      if (daySchedule.open && !timeRegex.test(daySchedule.open)) {
        errors.push(`${day}: Formato de hora de apertura inválido (use HH:MM)`);
      }
      if (daySchedule.close && !timeRegex.test(daySchedule.close)) {
        errors.push(`${day}: Formato de hora de cierre inválido (use HH:MM)`);
      }
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors
  };
}; 