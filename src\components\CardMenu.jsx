import React from 'react';
import styled from 'styled-components';
import * as Icons from 'lucide-react';
import { motion } from 'framer-motion';
import { sectionStyles } from '../utils/sectionStyles';
import { useNavigate } from 'react-router-dom';

const Card = styled(motion.div)`
  background: white;
  border-radius: 20px;
  padding: 1.5rem;
  margin: 1rem;
  width: 180px;
  height: 180px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: center;
  transition: box-shadow 0.3s ease-in-out;
  cursor: pointer;

  &:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  }
`;

const IconWrapper = styled.div`
  margin-bottom: 1rem;
`;

const Title = styled.h3`
  font-size: 1rem;
  margin: 0;
`;

const routeMap = {
  'Conferencistas': '/conferencistas',
  'Actividades': '/events',
  'Emprendedores': '/emprendedores',
  'Captura de reseñas': '/reseñas',
  'Mapa Interactivo': '/map-plant1',
  'Stands Virtuales': '/stands-virtuales'
};

export const CardMenu = ({ title }) => {
  const navigate = useNavigate();
  const section = sectionStyles[title];
  const LucideIcon = Icons[section?.icon || 'Square'];

  const handleClick = () => {
    const route = routeMap[title];
    if (route) {
      navigate(route);
    }
  };

  return (
    <Card
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ scale: 1.07 }}
      transition={{ duration: 0.4, ease: 'easeOut' }}
      onClick={handleClick}
    >
      <IconWrapper>
        <LucideIcon size={32} color={section?.color || '#000'} />
      </IconWrapper>
      <Title>{title}</Title>
    </Card>
  );
};

