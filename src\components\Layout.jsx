import React, { useEffect } from 'react';
import styled from 'styled-components';
import { Navbar } from './Navbar';
import { Footer } from './Footer';
import { setJsonLd } from '../utils/seo';

const LayoutContainer = styled.div`
  min-height: 100vh;
  display: flex;
  flex-direction: column;
`;

const MainContent = styled.main`
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const Layout = ({ children, showNavbar = true, showFooter = true, isLoggedIn = false, onLoginClick }) => {
  // Apply organization and website JSON-LD once on mount
  useEffect(() => {
    const origin = 'https://www.themercadodistrict.com/';

    setJsonLd('ld-website', {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      url: origin,
      name: 'Mercado District',
      potentialAction: {
        '@type': 'SearchAction',
        target: `${origin}stands-virtuales?q={search_term_string}`,
        'query-input': 'required name=search_term_string'
      }
    });

    setJsonLd('ld-organization', {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: 'Mercado District',
      url: origin,
      logo: `${origin}mercado%20favicon.png`
    });
  }, []);

  return (
    <LayoutContainer>
      {showNavbar && (
        <Navbar 
          isLoggedIn={isLoggedIn} 
          onLoginClick={onLoginClick} 
        />
      )}
      <MainContent>
        {children}
      </MainContent>
      {showFooter && <Footer />}
    </LayoutContainer>
  );
};

export default Layout; 