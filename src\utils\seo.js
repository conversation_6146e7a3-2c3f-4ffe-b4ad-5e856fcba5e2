// SEO utilities to manage JSON-LD and canonical links across routes

/**
 * Insert or replace a JSON-LD script tag in <head>
 * @param {string} id - unique element id
 * @param {object} data - JSON-LD object
 */
export function setJsonLd(id, data) {
  try {
    const existing = document.getElementById(id);
    if (existing && existing.parentNode) existing.parentNode.removeChild(existing);

    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.id = id;
    script.text = JSON.stringify(data);
    document.head.appendChild(script);
  } catch {
    // no-op in non-DOM environments
  }
}

/**
 * Ensure a single canonical link exists and points to the given URL
 * @param {string} href
 */
export function setCanonical(href) {
  try {
    const id = 'canonical-link';
    let link = document.getElementById(id);
    if (!link) {
      link = document.createElement('link');
      link.rel = 'canonical';
      link.id = id;
      document.head.appendChild(link);
    }
    link.setAttribute('href', href);
  } catch {
    // ignore when document/head is not available
  }
}

/**
 * Build and apply a simple breadcrumb list with Home + current page
 * @param {string} pageUrl - absolute URL of current page
 * @param {string} pageName - readable page name
 * @param {string} [id='ld-breadcrumbs'] - element id
 */
export function setBreadcrumbs(pageUrl, pageName, id = 'ld-breadcrumbs') {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'Home',
        item: 'https://www.themercadodistrict.com/'
      },
      {
        '@type': 'ListItem',
        position: 2,
        name: pageName,
        item: pageUrl
      }
    ]
  };
  setJsonLd(id, data);
}


