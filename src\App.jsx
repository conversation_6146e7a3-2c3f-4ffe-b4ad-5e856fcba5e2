import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import { useEffect, Suspense, lazy } from 'react'
import { useAuthStore } from './store/useAuthStore'
import { useUserStore } from './store/useUserStore'
import { Toaster } from 'react-hot-toast';
import LoadingSpinner from './components/LoadingSpinner'
import PasswordResetRedirect from './components/PasswordResetRedirect'
import ProtectedRoute from './components/ProtectedRoute'
import Layout from './components/Layout'
import { preloadCriticalImages } from './utils/imageOptimization'

// Lazy load all pages for better performance
const Login = lazy(() => import('./pages/Login'))
const Home = lazy(() => import('./pages/Home'))
const Register = lazy(() => import('./pages/Register'))
const VerifyEmail = lazy(() => import('./pages/VerifyEmail'))
const ForgotPassword = lazy(() => import('./pages/ForgotPassword'))
const ResetPassword = lazy(() => import('./pages/ResetPassword'))
const StandsVirtuales = lazy(() => import('./pages/StandsVirtuales'))
const StandVirtualDetalle = lazy(() => import('./pages/StandVirtualDetalle'))
const ReproducirVideo = lazy(() => import('./pages/ReproducirVideo'))
const PerfilUsuario = lazy(() => import('./pages/PerfilUsuario'))
const MyPanel = lazy(() => import('./pages/MyPanel'))
const AdminPanel = lazy(() => import('./pages/AdminPanel'))
const Events = lazy(() => import('./pages/Events'))
const EventDetails = lazy(() => import('./pages/EventDetails'))
const AboutUs = lazy(() => import('./pages/AboutUs'))
const Hours = lazy(() => import('./pages/Hours'))
const MapPlant1 = lazy(() => import('./components/MapPlant1'))
const TenantDetail = lazy(() => import('./pages/TenantDetail'))

import './App.css'

function App() {
  const checkUser = useAuthStore((state) => state.checkUser)
  const setUser = useAuthStore((state) => state.setUser)
  const initializeAuthListener = useAuthStore((state) => state.initializeAuthListener)
  const fetchUserData = useUserStore((state) => state.fetchUserData)

  useEffect(() => {
    // Initialize authentication state and listener on app startup
    const initializeAuth = async () => {
      try {
        // Set up the auth state change listener
        initializeAuthListener()
        
        // Check current user session
        const { user } = await checkUser()
        if (user) {
          setUser(user)
          // Fetch additional user data if authenticated
          await fetchUserData(user.id)
        }
      } catch (error) {
        console.error('Error initializing authentication:', error)
      }
    }

    // Preload critical images for better performance
    preloadCriticalImages()

    initializeAuth()
  }, [checkUser, setUser, initializeAuthListener, fetchUserData])

  return (
    <>
      <BrowserRouter>
        <Toaster position="top-center" reverseOrder={false} />
        <Suspense fallback={<LoadingSpinner message="Cargando página..." />}>
          <Routes>
            <Route path="/" element={
              <>
                <PasswordResetRedirect />
                <Layout>
                  <Home />
                </Layout>
              </>
            } />
            <Route path="/login" element={<Login/>} />
            <Route path="/signup" element={<Register />} />
            <Route path="/verifica-tu-correo" element={<VerifyEmail />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/reset-password" element={<ResetPassword />} />
            <Route path='/map-plant1' element={<MapPlant1 />} />
            <Route path='/stands-virtuales' element={
              <Layout>
                <StandsVirtuales />
              </Layout>
            } />
            <Route path='/stands-virtuales/:slug' element={
              <Layout>
                <StandVirtualDetalle />
              </Layout>
            } />
            <Route path='/reproducir/:id' element={
              <Layout>
                <ReproducirVideo />
              </Layout>
            } />
            <Route path='/perfil/:id' element={
              <ProtectedRoute>
                <Layout>
                  <PerfilUsuario />
                </Layout>
              </ProtectedRoute>
            } />
            <Route path='/dashboard/*' element={
              <ProtectedRoute>
                <MyPanel />
              </ProtectedRoute>
            } />
            <Route path='/admin/*' element={
              <ProtectedRoute>
                <AdminPanel />
              </ProtectedRoute>
            } />
            <Route path='/events' element={
              <Layout>
                <Events />
              </Layout>
            } />
            <Route path='/eventinfo/:id' element={
              <Layout>
                <EventDetails />
              </Layout>
            } />
            <Route path='/aboutus' element={
              <Layout>
                <AboutUs />
              </Layout>
            } />
            <Route path='/hours' element={
              <Layout>
                <Hours />
              </Layout>
            } />
            <Route path='/tenant/:slug' element={
              <Layout>
                <TenantDetail />
              </Layout>
            } />
            
            {/* Redirect all unknown routes to home */}
            <Route path='*' element={<Navigate to="/" replace />} />
          </Routes>
        </Suspense>
      </BrowserRouter>
    </>
  )
}

export default App
