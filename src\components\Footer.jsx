import React from "react";
import styled from "styled-components";
import { useLanguageStore } from "../store/useLanguageStore";
import { Clock } from "lucide-react";

const FooterContainer = styled.footer`
  color: white;
  padding: 4rem 2rem;
  position: relative;
  overflow: hidden;
  
  /* Blurred background image */
  &::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    width: calc(100% + 20px);
    height: calc(100% + 20px);
    background: url('/banner.webp') center/cover no-repeat;
    background-size: cover;
    filter: blur(3px);
    z-index: 1;
  }
  
  /* Dark overlay with original footer color */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.85));
    z-index: 2;
  }
`;

const FooterContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 3rem;
  position: relative;
  z-index: 3;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
  }
`;

const FooterSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const FooterTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  color: #f16925;
`;

const FooterLink = styled.a`
  color: #ccc;
  text-decoration: none;
  transition: color 0.3s ease;
  font-size: 1rem;

  &:hover {
    color: #f16925;
  }
`;

const FooterText = styled.p`
  color: #ccc;
  margin: 0;
  line-height: 1.6;
  font-size: 1rem;
`;

const SocialLinks = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1rem;

  @media (max-width: 768px) {
    justify-content: center;
  }
`;

const SocialIcon = styled.a`
  color: #ccc;
  font-size: 1.5rem;
  transition: color 0.3s ease;

  &:hover {
    color: #f16925;
  }
`;

const Copyright = styled.div`
  text-align: center;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #333;
  color: #666;
  font-size: 0.9rem;
  position: relative;
  z-index: 3;
`;

const BusinessHoursContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const HoursRow = styled.div`
  display: flex;
  justify-content: space-between;
  color: #ccc;
  font-size: 1rem;
`;

const DayLabel = styled.span`
  font-weight: 600;
`;

export const Footer = () => {
  const { language } = useLanguageStore();
  return (
    <FooterContainer>
      <FooterContent>
        <FooterSection>
          <FooterTitle>Mercado Food Hall</FooterTitle>
          <FooterText>
            {language === "es"
              ? "Descubra infinitas posibilidades en Mercado Food Hall & Shops, donde delicias culinarias, compras únicas y eventos vibrantes, música en vivo y joyas ocultas se unen para una experiencia inolvidable."
              : "Discover endless possibilities at Mercado Food Hall & Shops—where culinary delights, unique shopping and vibrant events, live music, and hidden gems come together for an unforgettable experience."}
          </FooterText>
          <SocialLinks>
            <SocialIcon
              href="#" /* TODO: replace with real link */
              aria-label="Facebook"
              target="_blank"
              rel="noopener noreferrer"
            >
              {/* Icon is decorative – hide from assistive tech */}
              <i aria-hidden="true" className="fab fa-facebook"></i>
            </SocialIcon>
            <SocialIcon
              href="#"
              aria-label="Instagram"
              target="_blank"
              rel="noopener noreferrer"
            >
              <i aria-hidden="true" className="fab fa-instagram"></i>
            </SocialIcon>
            <SocialIcon
              href="#"
              aria-label="Twitter"
              target="_blank"
              rel="noopener noreferrer"
            >
              <i aria-hidden="true" className="fab fa-twitter"></i>
            </SocialIcon>
          </SocialLinks>
        </FooterSection>

        <FooterSection>
          <FooterTitle>
            {language === "es" ? "Enlaces Rápidos" : "Quick Links"}
          </FooterTitle>
          <FooterLink href="/stands-virtuales">
            {language === "es" ? "Stands Virtuales" : "Virtual Markets"}
          </FooterLink>
          <FooterLink href="/dashboard">
            {language === "es" ? "Panel de Control" : "Dashboard"}
          </FooterLink>
          <FooterLink href="/login">
            {language === "es" ? "Iniciar Sesión" : "Sign In"}
          </FooterLink>
        </FooterSection>

        <FooterSection>
          <FooterTitle>
            {language === "es" ? "Contacto" : "Contact"}
          </FooterTitle>
          <FooterText>
            {" "}
            Email: <EMAIL>
            <br />
            {language === "es"
              ? "Teléfono: (*************"
              : "Phone: (*************"}
            <br />
            {language === "es"
              ? "Dirección: 4400 N 23rd St, McAllen, TX 78504"
              : "Address: 4400 N 23rd St, McAllen, TX 78504"}
          </FooterText>
        </FooterSection>

        <FooterSection>
          <FooterTitle>
            <Clock size={20} style={{ marginRight: "8px" }} />
            {language === "es" ? "Horario" : "Business Hours"}
          </FooterTitle>
          <BusinessHoursContainer>
            <HoursRow>
              <DayLabel>{language === "es" ? "Lunes" : "Monday"}</DayLabel>
              <span>{language === "es" ? "Cerrado" : "Closed"}</span>
            </HoursRow>
            <HoursRow>
              <DayLabel>
                {language === "es" ? "Martes - Jueves" : "Tuesday - Thursday"}
              </DayLabel>
              <span>11:00 AM - 10:00 PM</span>
            </HoursRow>
            <HoursRow>
              <DayLabel>
                {language === "es" ? "Viernes - Sábado" : "Friday - Saturday"}
              </DayLabel>
              <span>11:00 AM - 11:00 PM</span>
            </HoursRow>
            <HoursRow>
              <DayLabel>{language === "es" ? "Domingo" : "Sunday"}</DayLabel>
              <span>11:00 AM - 6:00 PM</span>
            </HoursRow>
          </BusinessHoursContainer>
        </FooterSection>
      </FooterContent>

      <Copyright>
        © {new Date().getFullYear()} Mercado Food Hall.{" "}
        {language === "es"
          ? "Todos los derechos reservados."
          : "All rights reserved."}
      </Copyright>
    </FooterContainer>
  );
};
