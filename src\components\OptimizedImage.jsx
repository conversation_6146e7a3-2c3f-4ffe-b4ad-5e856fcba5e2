import React from 'react';
import { getOptimizedImageProps, LARGE_IMAGES, optimizeSupabaseImageUrl, getResponsiveImageAttributes } from '../utils/imageOptimization';

const OptimizedImage = ({
  src,
  alt,
  loading = 'lazy',
  className = '',
  style = {},
  type = 'default',
  width,
  height,
  quality = 80,
  ...props
}) => {
  // Check if this image has optimized versions
  const optimizedImage = LARGE_IMAGES[src];

  if (optimizedImage) {
    return (
      <picture>
        {/* WebP source for modern browsers */}
        <source
          srcSet={optimizedImage.optimized}
          type="image/webp"
          sizes={optimizedImage.sizes}
        />

        {/* Fallback for older browsers */}
        <img
          src={optimizedImage.fallback}
          alt={alt}
          loading={loading}
          decoding="async"
          fetchpriority={loading === 'eager' ? 'high' : 'auto'}
          className={className}
          style={style}
          sizes={optimizedImage.sizes}
          {...props}
        />
      </picture>
    );
  }

  // Handle Supabase images with optimization
  if (src && src.includes('supabase')) {
    const optimizedSrc = optimizeSupabaseImageUrl(src, { width, height, quality });
    const responsiveAttrs = getResponsiveImageAttributes(src, type);

    return (
      <img
        src={optimizedSrc}
        alt={alt}
        loading={loading}
        className={className}
        style={style}
        {...responsiveAttrs}
        {...props}
      />
    );
  }
  
  // For non-optimized images, use regular img with optimized props
  const imageProps = getOptimizedImageProps(src, alt, loading);
  
  return (
    <img 
      {...imageProps}
      className={className}
      style={style}
      {...props}
    />
  );
};

export default OptimizedImage; 