import React, { useState } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { 
  Upload,
  X,
  Image,
  Trash
} from 'lucide-react';
import { supabase } from '../../utils/supabaseClient';
import { toast } from 'react-hot-toast';

const Modal = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
`;

const ModalContent = styled.div`
  background: white;
  border-radius: 12px;
  padding: 2rem;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
`;

const ModalTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0.5rem;

  &:hover {
    color: #1a1a1a;
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
`;

const Input = styled.input`
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #f16925;
  }
`;

const Select = styled.select`
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: #f16925;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  justify-content: flex-end;
`;

const Button = styled.button`
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &.primary {
    background: #f16925;
    color: white;

    &:hover {
      background: #e05a1a;
      transform: translateY(-1px);
    }
  }

  &.secondary {
    background: #6b7280;
    color: white;

    &:hover {
      background: #4b5563;
      transform: translateY(-1px);
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const FileUploadContainer = styled.div`
  border: 2px dashed #e1e5e9;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  background: #fafafa;

  &:hover {
    border-color: #f16925;
    background: #fef7f4;
  }

  &.has-file {
    border-color: #10b981;
    background: #f0fdf4;
  }
`;

const FileUploadIcon = styled.div`
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: #6b7280;
`;

const FileUploadText = styled.div`
  color: #374151;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
`;

const FileUploadHint = styled.div`
  color: #6b7280;
  font-size: 0.8rem;
`;

const FileInput = styled.input`
  display: none;
`;

const FilePreview = styled.div`
  margin-top: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
`;

const FilePreviewImage = styled.img`
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 6px;
`;

const FileInfo = styled.div`
  flex: 1;
`;

const FileName = styled.div`
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
`;

const FileSize = styled.div`
  color: #6b7280;
  font-size: 0.8rem;
`;

const RemoveFileButton = styled.button`
  background: #ef4444;
  color: white;
  border: none;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #dc2626;
    transform: translateY(-1px);
  }
`;

const UploadProgress = styled.div`
  margin-top: 1rem;
  background: #f3f4f6;
  border-radius: 8px;
  overflow: hidden;
`;

const ProgressBar = styled.div`
  height: 4px;
  background: linear-gradient(135deg, #f16925, #e05a1a);
  width: ${props => props.progress}%;
  transition: width 0.3s ease;
`;

const ProgressText = styled.div`
  padding: 0.5rem;
  font-size: 0.8rem;
  color: #374151;
  text-align: center;
`;

const TenantForm = ({ 
  isOpen, 
  onClose, 
  onSubmit, 
  editingTenant, 
  onSuccess 
}) => {
  const [formData, setFormData] = useState({
    tenant_name: '',
    unit: '',
    unit_type: '',
    phone_default: '',
    category: '',
    avatar: '',
    banner_url: '',
    slug: ''
  });
  const [avatarFile, setAvatarFile] = useState(null);
  const [bannerFile, setBannerFile] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  // File upload constants
  const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  const ALLOWED_FORMATS = ['image/png', 'image/jpeg', 'image/webp'];

  // Initialize form data when editing
  React.useEffect(() => {
    if (editingTenant) {
      setFormData({
        tenant_name: editingTenant.tenant_name || '',
        unit: editingTenant.unit || '',
        unit_type: editingTenant.unit_type || '',
        phone_default: editingTenant.phone_default || '',
        category: editingTenant.category || '',
        avatar: editingTenant.avatar || '',
        banner_url: editingTenant.banner_url || '',
        slug: editingTenant.slug || '',
      });
    } else {
      setFormData({
        tenant_name: '',
        unit: '',
        unit_type: '',
        phone_default: '',
        category: '',
        avatar: '',
        banner_url: '',
        slug: '',
      });
    }
    setAvatarFile(null);
    setBannerFile(null);
  }, [editingTenant, isOpen]);

  const validateFile = (file) => {
    if (!ALLOWED_FORMATS.includes(file.type)) {
      toast.error('Please select a PNG, JPG, or WebP image file');
      return false;
    }
    
    if (file.size > MAX_FILE_SIZE) {
      toast.error(`File size must be less than ${(MAX_FILE_SIZE / 1024 / 1024).toFixed(1)}MB`);
      return false;
    }
    
    return true;
  };

  const handleFileSelect = (e, type) => {
    const file = e.target.files[0];
    if (!file) return;

    if (!validateFile(file)) {
      e.target.value = '';
      return;
    }

    if (type === 'avatar') {
      setAvatarFile(file);
      setFormData(prev => ({ ...prev, avatar: '' }));
    } else if (type === 'banner') {
      setBannerFile(file);
      setFormData(prev => ({ ...prev, banner_url: '' }));
    }
  };

  const removeFile = (type) => {
    if (type === 'avatar') {
      setAvatarFile(null);
      setFormData(prev => ({ ...prev, avatar: '' }));
    } else if (type === 'banner') {
      setBannerFile(null);
      setFormData(prev => ({ ...prev, banner_url: '' }));
    }
  };

  const uploadFile = async (file, type) => {
    try {
      setIsUploading(true);
      setUploadProgress(0);

      const fileExt = file.name.split('.').pop();
      const fileName = `${type}_${Date.now()}.${fileExt}`;
      const filePath = `tenant-images/${fileName}`;

      const { error } = await supabase.storage
        .from('images')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) throw error;

      const { data: urlData } = supabase.storage
        .from('images')
        .getPublicUrl(filePath);

      setUploadProgress(100);
      return urlData.publicUrl;
    } catch (error) {
      toast.error(`Error uploading ${type} image`);
      throw error;
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.tenant_name.trim()) {
      toast.error('Tenant name is required');
      return;
    }
    
    if (!formData.unit.trim()) {
      toast.error('Unit is required');
      return;
    }
    
    if (!formData.category.trim()) {
      toast.error('Category is required');
      return;
    }
    
    try {
      setIsUploading(true);
      let finalFormData = { ...formData };

      // Upload avatar if selected
      if (avatarFile) {
        const avatarUrl = await uploadFile(avatarFile, 'avatar');
        finalFormData.avatar = avatarUrl;
      }

      // Upload banner if selected
      if (bannerFile) {
        const bannerUrl = await uploadFile(bannerFile, 'banner');
        finalFormData.banner_url = bannerUrl;
      }

      // Call the parent onSubmit function
      await onSubmit(finalFormData, editingTenant);
      
      // Reset form and close modal
      setFormData({
        tenant_name: '',
        unit: '',
        unit_type: '',
        phone_default: '',
        category: '',
        avatar: '',
        banner_url: '',
        slug: ''
      });
      setAvatarFile(null);
      setBannerFile(null);
      onSuccess();
    } catch (error) {
      console.error('Form submission error:', error);
      toast.error(editingTenant ? 'Error updating tenant listing' : 'Error creating tenant listing');
    } finally {
      setIsUploading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const renderFileUpload = (type, currentFile, currentUrl) => {
    const hasFile = currentFile || currentUrl;
    const file = currentFile;
    const url = currentUrl;

    return (
      <FormGroup>
        <Label>{type === 'avatar' ? 'Avatar Image' : 'Banner Image'}</Label>
        
        {!hasFile ? (
          <FileUploadContainer
            onClick={() => document.getElementById(`${type}-input`).click()}
          >
            <FileUploadIcon>
              <Image />
            </FileUploadIcon>
            <FileUploadText>Click to upload image</FileUploadText>
            <FileUploadHint>
              PNG, JPG, or WebP up to {(MAX_FILE_SIZE / 1024 / 1024).toFixed(1)}MB
            </FileUploadHint>
            <FileInput
              id={`${type}-input`}
              type="file"
              accept=".png,.jpg,.jpeg,.webp"
              onChange={(e) => handleFileSelect(e, type)}
            />
          </FileUploadContainer>
        ) : (
          <div>
            <FilePreview>
              {file ? (
                <>
                  <FilePreviewImage
                    src={URL.createObjectURL(file)}
                    alt="Preview"
                  />
                  <FileInfo>
                    <FileName>{file.name}</FileName>
                    <FileSize>{formatFileSize(file.size)}</FileSize>
                  </FileInfo>
                  <RemoveFileButton
                    onClick={() => removeFile(type)}
                    title="Remove file"
                  >
                    <Trash size={16} />
                  </RemoveFileButton>
                </>
              ) : url ? (
                <>
                  <FilePreviewImage
                    src={url}
                    alt="Current image"
                  />
                  <FileInfo>
                    <FileName>Current image</FileName>
                    <FileSize>Click upload to replace</FileSize>
                  </FileInfo>
                  <RemoveFileButton
                    onClick={() => {
                      if (type === 'avatar') {
                        setFormData(prev => ({ ...prev, avatar: '' }));
                      } else {
                        setFormData(prev => ({ ...prev, banner_url: '' }));
                      }
                    }}
                    title="Remove current image"
                  >
                    <Trash size={16} />
                  </RemoveFileButton>
                </>
              ) : null}
            </FilePreview>
            
            {!file && (
              <FileUploadContainer
                onClick={() => document.getElementById(`${type}-input`).click()}
                style={{ marginTop: '1rem' }}
              >
                <FileUploadIcon>
                  <Upload />
                </FileUploadIcon>
                <FileUploadText>Click to upload new image</FileUploadText>
                <FileInput
                  id={`${type}-input`}
                  type="file"
                  accept=".png,.jpg,.jpeg,.webp"
                  onChange={(e) => handleFileSelect(e, type)}
                />
              </FileUploadContainer>
            )}
          </div>
        )}

        {isUploading && uploadProgress > 0 && (
          <UploadProgress>
            <ProgressBar progress={uploadProgress} />
            <ProgressText>Uploading... {uploadProgress}%</ProgressText>
          </UploadProgress>
        )}
      </FormGroup>
    );
  };

  if (!isOpen) return null;

  return (
    <Modal
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <ModalContent>
        <ModalHeader>
          <ModalTitle>
            {editingTenant ? 'Edit Tenant Listing' : 'Create New Tenant Listing'}
          </ModalTitle>
          <CloseButton onClick={onClose}>
            <X />
          </CloseButton>
        </ModalHeader>

        <Form onSubmit={handleSubmit}>
          <FormGroup>
            <Label>Tenant Name *</Label>
            <Input
              type="text"
              name="tenant_name"
              value={formData.tenant_name}
              onChange={handleInputChange}
              required
              placeholder="Enter tenant name"
            />
          </FormGroup>

          <FormGroup>
            <Label>Unit</Label>
            <Input
              type="text"
              name="unit"
              value={formData.unit}
              onChange={handleInputChange}
              placeholder="Enter unit number/name"
            />
          </FormGroup>

          <FormGroup>
            <Label>Unit Type</Label>
            <Input
              type="text"
              name="unit_type"
              value={formData.unit_type}
              onChange={handleInputChange}
              placeholder="e.g., Kiosk, Store, Booth"
            />
          </FormGroup>

          <FormGroup>
            <Label>Phone</Label>
            <Input
              type="tel"
              name="phone_default"
              value={formData.phone_default}
              onChange={handleInputChange}
              placeholder="Enter phone number"
            />
          </FormGroup>

          <FormGroup>
            <Label>Category</Label>
            <Select
              name="category"
              value={formData.category}
              onChange={handleInputChange}
            >
              <option value="">Select category</option>
              <option value="Retail">Retail</option>
              <option value="Beauty">Beauty</option>
            </Select>
          </FormGroup>

          <FormGroup>
            <Label>Slug</Label>
            <Input
              type="text"
              name="slug"
              value={formData.slug}
              onChange={handleInputChange}
              placeholder="Enter URL slug (optional)"
            />
          </FormGroup>

          {renderFileUpload('avatar', avatarFile, formData.avatar)}
          {renderFileUpload('banner', bannerFile, formData.banner_url)}

          <ButtonGroup>
            <Button
              type="button"
              className="secondary"
              onClick={onClose}
              disabled={isUploading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="primary"
              disabled={isUploading}
            >
              {isUploading ? 'Uploading...' : (editingTenant ? 'Update' : 'Create')}
            </Button>
          </ButtonGroup>
        </Form>
      </ModalContent>
    </Modal>
  );
};

export default TenantForm;
