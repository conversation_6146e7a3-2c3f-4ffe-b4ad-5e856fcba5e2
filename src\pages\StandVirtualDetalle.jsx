import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import styled, { keyframes } from 'styled-components';
import { supabase } from '../utils/supabaseClient';
import { Mail, Phone, ArrowLeft, Instagram, Facebook, Linkedin, ExternalLink, Clock } from 'lucide-react';
import { useLanguageStore } from '../store/useLanguageStore';
import ChatbotWidget from '../components/ChatbotWidget';
import ProductCard from '../components/ProductCard';
import useAnalytics from '../hooks/useAnalytics';
import Loader from '../components/Loader';
import { useStandsStore } from '../store/useStandsStore';
import { getFormattedHours, getStoreStatus } from '../data/businessHours';

const PageWrapper = styled.div`
  background-color: #eaeaf2;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  color: #333;
`;

const BannerContainer = styled.div`
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;

  @media (max-width: 768px) {
    height: 200px;
  }
`;

const Banner = styled.div`
  width: 100%;
  height: 100%;
  background-color: #f8f9fa;
  position: relative;
`;

const BannerImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
  
  &.loading {
    opacity: 0;
  }
  
  &.loaded {
    opacity: 1;
  }
`;

const BannerOverlay = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.9), transparent);
  padding: 2rem;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const TopBannerOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 1rem 2rem;
  z-index: 10;

  @media (max-width: 768px) {
    padding: 0.5rem 1rem;
  }
`;

const StandName = styled.h1`
  margin: 0;
  font-size: 2.5rem;
  font-weight: 700;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);

  @media (max-width: 768px) {
    font-size: 1.8rem;
  }
`;

const CategoryBadge = styled.span`
  background-color: #F16925;
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-top: 1rem;

  @media (max-width: 768px) {
    font-size: 0.8rem;
    padding: 0.2rem 0.6rem;
  }
`;

const UnitBadge = styled.span`
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 0.25rem 0.7rem;
  border-radius: 18px;
  font-size: 0.85rem;
  font-weight: 500;
  margin-top: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.3);

  @media (max-width: 768px) {
    font-size: 0.75rem;
  }
`;

const ContentWrapper = styled.div`
  display: grid;
  grid-template-columns: 1fr 3fr;
  gap: 2rem;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    padding: 1rem;
    gap: 1rem;
  }
`;

const Sidebar = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  @media (max-width: 768px) {
    gap: 1rem;
  }
`;

const ProfileCard = styled.div`
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  margin-top: -50px;
  border: 1px solid #eee;

  @media (max-width: 768px) {
    margin-top: -30px;
    padding: 1rem;
  }
`;

const Avatar = styled.img`
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 3px solid #F16925;
  margin-top: -60px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background-color: white;
  transition: opacity 0.3s ease;
  object-fit: cover;

  &.loading {
    opacity: 0;
  }
  
  &.loaded {
    opacity: 1;
  }

  @media (max-width: 768px) {
    width: 100px;
    height: 100px;
    margin-top: -50px;
  }
`;

const ContactCard = styled.div`
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #eee;

  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const ContactItem = styled.a`
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.8rem;
  border-radius: 8px;
  color: #333;
  text-decoration: none;
  transition: all 0.3s ease;
  background: #f8f8f8;
  border: 1px solid #eee;
  &:hover {
    background: #F16925;
    color: #fff;
    border-color: #F16925;
  }

  @media (max-width: 768px) {
    padding: 0.6rem;
  }
`;

const ContactTitle = styled.h3`
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  color: #333;

  @media (max-width: 768px) {
    font-size: 1.1rem;
  }
`;

const MainContent = styled.div`
  background: white;
  border-radius: 10px;
  padding: 2rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #eee;

  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const SectionTitle = styled.h2`
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.8rem;
  position: relative;
  padding-bottom: 0.5rem;

  &:after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 4px;
    background-color: #F16925;
  }

  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
`;

const Description = styled.div`
  line-height: 1.7;
  color: #666;
  white-space: pre-wrap;

  @media (max-width: 768px) {
    font-size: 0.9rem;
  }
`;

const BackButton = styled(Link)`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.2rem;  background-color: #F16925;
  color: #fff;  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #F16925;

  &:hover {
    background: #ff8b5c;
    transform: translateY(-2px);
  }

  @media (max-width: 768px) {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }
`;

const WhatsAppButton = styled.a`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: #25D366;
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  margin-top: 1.5rem;
  transition: all 0.3s ease;

  &:hover {
    background-color: #128C7E;
    transform: translateY(-2px);
  }

  @media (max-width: 768px) {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }
`;

const SocialMediaContainer = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  justify-content: center;

  @media (max-width: 768px) {
    margin-top: 1rem;
  }
`;

const SocialIcon = styled.a`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f8f8f8;
  color: #333;
  transition: all 0.3s ease;
  border: 1px solid #eee;

  &:hover {
    transform: translateY(-3px);    background: #F16925;
    color: #fff;
    border-color: #F16925;
  }

  @media (max-width: 768px) {
    width: 35px;
    height: 35px;
  }
`;

const DigitalCardButton = styled.a`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: #4A5568;
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  margin-top: 1rem;
  transition: all 0.3s ease;

  &:hover {
    background-color: #2D3748;
    transform: translateY(-2px);
  }

  @media (max-width: 768px) {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }
`;

const PostsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
`;

const PostCard = styled.div`
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
`;

const PostImage = styled.div`
  width: 100%;
  height: 200px;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  background-color: #f8f8f8;
  position: relative;
`;

const PostContent = styled.div`
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const PostTitle = styled.h3`
  margin: 0;
  font-size: 1.2rem;
  color: #333;
  font-weight: 600;
`;

const PostDescription = styled.p`
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex-grow: 1;
`;

const PostFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
`;

const PostDate = styled.span`
  color: #999;
  font-size: 0.8rem;
`;

const PostPrice = styled.span`  color: #F16925;
  font-size: 1.1rem;
  font-weight: 600;
  background: rgba(241, 105, 37, 0.1);
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
`;

const MenuBadge = styled.span`
  position: absolute;
  top: 1rem;
  right: 1rem;  background: #F16925;
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 1;
`;

const ExpandedPost = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  overflow-y: auto;
`;

const ExpandedContent = styled.div`
  background: white;
  max-width: 800px;
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
`;

const CloseButton = styled.button`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  z-index: 1001;
  padding: 0.5rem;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: rgba(0, 0, 0, 0.7);
  }
`;

const ExpandedImage = styled.div`
  width: 100%;
  height: 400px;
  background-size: cover;
  background-position: center;
`;

const ExpandedText = styled.div`
  padding: 2rem;
`;

const ExpandedTitle = styled.h2`
  margin: 0 0 1rem;
  font-size: 2rem;
  color: #333;
`;

const ExpandedDescription = styled.p`
  margin: 0;
  color: #666;
  line-height: 1.6;
  font-size: 1.1rem;
`;

const ExpandedDate = styled.span`
  display: block;
  margin-top: 1.5rem;
  color: #999;
  font-size: 0.9rem;
`;

const BusinessHoursCard = styled.div`
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
`;

const BusinessHoursTitle = styled.h3`
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #333;
`;

const HoursContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const HoursRow = styled.div`
  display: flex;
  justify-content: space-between;
  font-size: 0.95rem;
  color: #666;
`;

const DayLabel = styled.span`
  font-weight: 600;
  color: #333;
`;

// Skeleton animation for loading images
const shimmer = keyframes`
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
`;

// Placeholder component displayed while banner image loads
const SkeletonImage = styled.div`
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #e0e0e0 25%, #f5f5f5 37%, #e0e0e0 63%);
  background-size: 1000px 100%;
  animation: ${shimmer} 1.6s linear infinite;
`;

const StandVirtualDetalle = () => {
  const { slug } = useParams();
  const { stands: rawStands, fetchStands } = useStandsStore();
  const [stand, setStand] = useState(null);
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [expandedPost, setExpandedPost] = useState(null);
  const [imageLoaded, setImageLoaded] = useState({});
  useAnalytics(); // initialize analytics hook
  const { language } = useLanguageStore();

  const handleImageLoad = (imageType) => {
    setImageLoaded(prev => ({
      ...prev,
      [imageType]: true
    }));
  };

  const content = {
    backToExplore: language === 'es' ? 'Regresar a Explorar' : 'Back to Explore',
    contactInfo: language === 'es' ? 'Información de Contacto' : 'Contact Information',
    about: language === 'es' ? 'Acerca de' : 'About',
    noDescription: language === 'es' ? 'No hay descripción disponible para este stand.' : 'No description available for this stand.',
    promotions: language === 'es' ? 'Promociones, Productos y Publicaciones' : 'Promotions, Products and Posts',
    contactWhatsApp: language === 'es' ? 'Contactar por WhatsApp' : 'Contact via WhatsApp',
    viewDigitalCatalog: language === 'es' ? 'Ver Catálago Digital' : 'View Digital Catalog',
    error: language === 'es' ? 'No se pudo cargar la información del stand' : 'Could not load stand information',
    notFound: language === 'es' ? 'Stand no encontrado' : 'Stand not found'
  };

  const slugify = (str) => str.toLowerCase().trim().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');

  useEffect(() => {
    if (!rawStands.length) fetchStands();
  }, [rawStands.length, fetchStands]);

  useEffect(() => {
    const fetchStandDetails = async () => {
      if (stand) return;           // already have the stand ⇒ skip

      try {
        setLoading(true);
        if (rawStands.length) {
          const local = rawStands.find((s) => (s.slug || slugify(s.name)) === slug);
          if (local) {
            setStand(local);
          }
        }

        let selectedStand = stand;
        if (!selectedStand) {
          // First attempt: search by exact slug (returns an array)
          const { data: standArray, error: standError } = await supabase
            .from('stands')
            .select('*')
            .eq('slug', slug)
            .limit(1);

          if (standError) {
            throw standError;
          }

          let standData = (standArray && standArray.length) ? standArray[0] : null;

          // Fallback: perform a loose name match when slug is missing in DB
          if (!standData) {
            const namePattern = slug.replace(/-/g, ' ');
            const { data: nameMatches, error: nameError } = await supabase
              .from('stands')
              .select('*')
              .ilike('name', `%${namePattern}%`)
              .limit(1);

            if (nameError) {
              throw nameError;
            }

            standData = (nameMatches && nameMatches.length) ? nameMatches[0] : null;
          }

          if (standData) {
            selectedStand = standData;
            setStand(standData);
          } else {
            setError(content.notFound);
          }
        }

        if (!selectedStand) return;

        const { data: postsData, error: postsError } = await supabase
          .from('posts')
          .select('*')
          .eq('stand_id', selectedStand.id)
          .order('created_at', { ascending: false });

        if (postsError) {
          throw postsError;
        }

        setPosts(postsData);
      } catch (error) {
        console.error('Error fetching data:', error);
        setError(content.error);
      } finally {
        setLoading(false);
      }
    };

    fetchStandDetails();
  }, [slug, rawStands, stand, content.error, content.notFound]);

  const handleExpandPost = (post) => {
    setExpandedPost(post);
  };

  if (loading) {
    return <Loader />;
  }

  if (error || !stand) {
    return (
      <>
        <PageWrapper>
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh', flexDirection: 'column', gap: '1rem' }}>
            <p style={{ color: '#ff4444' }}>{error || content.notFound}</p>
            <BackButton to="/stands-virtuales">
              <ArrowLeft size={18} />
              {content.backToExplore}
            </BackButton>
          </div>
        </PageWrapper>
      </>
    );
  }

  const formattedWhatsApp = stand.whatsapp && stand.whatsapp.startsWith('+') 
    ? stand.whatsapp 
    : stand.whatsapp 
      ? `+${stand.whatsapp}` 
      : null;

  const InfoSection = () => {
    // Obtener horarios dinámicos basados en la fake API
    const businessHours = getFormattedHours(stand.name, stand.category, language);
    const currentStatus = getStoreStatus(stand.name, stand.category, language);

    return (
      <>
        <BusinessHoursCard>
          <BusinessHoursTitle>
            <Clock size={18} />
            {language === "es" ? "Horario" : "Business Hours"}
          </BusinessHoursTitle>
          <div style={{ 
            marginBottom: '1rem', 
            padding: '0.5rem 1rem', 
            borderRadius: '8px', 
            background: currentStatus.backgroundColor,
            color: currentStatus.color,
            fontWeight: '600',
            fontSize: '0.9rem',
            textAlign: 'center'
          }}>
            {currentStatus.status}
          </div>
          <HoursContainer>
            {businessHours.map((daySchedule, index) => (
              <HoursRow key={index}>
                <DayLabel>{daySchedule.day}</DayLabel>
                <span style={{ 
                  color: daySchedule.isOpen ? '#28a745' : '#dc3545',
                  fontWeight: daySchedule.isOpen ? '500' : '400'
                }}>
                  {daySchedule.hours}
                </span>
              </HoursRow>
            ))}
          </HoursContainer>
        </BusinessHoursCard>
      </>
    );
  };

  return (
    <>
      <PageWrapper>
        <BannerContainer>
          <Banner>
            {!imageLoaded.banner && <SkeletonImage />}
            <BannerImage 
              src={stand.banner_url} 
              alt={`${stand.name} banner`} 
              onLoad={() => handleImageLoad('banner')}
              style={{ display: imageLoaded.banner ? 'block' : 'none' }}
              loading="eager"
              fetchpriority="high"
            />
          </Banner>
          <TopBannerOverlay>
            <BackButton to="/stands-virtuales">
              <ArrowLeft size={18} />
              {content.backToExplore}
            </BackButton>
          </TopBannerOverlay>
          <BannerOverlay>
            <StandName>{stand.name}</StandName>
            {stand.category && <CategoryBadge>{stand.category}</CategoryBadge>}
            {stand.unit && <UnitBadge>{stand.unit}</UnitBadge>}
          </BannerOverlay>
        </BannerContainer>
        
        <ContentWrapper>
          <Sidebar>
            <ProfileCard>
              <Avatar 
                src={stand.avatar} 
                alt={stand.name}
                loading="lazy"
                className={imageLoaded.avatar ? 'loaded' : 'loading'}
                onLoad={() => handleImageLoad('avatar')}
              />
              <h2 style={{ marginTop: '1rem', textAlign: 'center', color: '#333' }}>{stand.name}</h2>
              
              <SocialMediaContainer>
                {stand.facebook && (
                  <SocialIcon 
                    href={stand.facebook} 
                    target="_blank" 
                    rel="noopener noreferrer"
                  >
                    <Facebook size={20} />
                  </SocialIcon>
                )}
                
                {stand.instagram && (
                  <SocialIcon 
                    href={stand.instagram} 
                    target="_blank" 
                    rel="noopener noreferrer"
                  >
                    <Instagram size={20} />
                  </SocialIcon>
                )}
                
                {stand.linkedin && (
                  <SocialIcon 
                    href={stand.linkedin} 
                    target="_blank" 
                    rel="noopener noreferrer"
                  >
                    <Linkedin size={20} />
                  </SocialIcon>
                )}
              </SocialMediaContainer>
              
              {formattedWhatsApp && (
                <WhatsAppButton 
                  href={`https://wa.me/${formattedWhatsApp.replace(/\+/g, '')}`}
                  target="_blank" 
                  rel="noopener noreferrer"
                >
                  <Phone size={18} />
                  {content.contactWhatsApp}
                </WhatsAppButton>
              )}
              
              {stand['tarjeta-digital'] && (
                <DigitalCardButton 
                  href={stand['tarjeta-digital']} 
                  target="_blank" 
                  rel="noopener noreferrer"
                >
                  <ExternalLink size={18} />
                  {content.viewDigitalCatalog}
                </DigitalCardButton>
              )}
            </ProfileCard>
            
            <ContactCard>
              <ContactTitle>{content.contactInfo}</ContactTitle>
              
              {stand.email && (
                <ContactItem href={`mailto:${stand.email}`}>
                  <Mail size={20} />
                  <span>{stand.email}</span>
                </ContactItem>
              )}
              
              {stand.whatsapp && (
                <ContactItem href={`tel:${stand.whatsapp}`}>
                  <Phone size={20} />
                  <span>{stand.whatsapp}</span>
                </ContactItem>
              )}
              {!stand.whatsapp && stand.phone_default && (
                <ContactItem href={`tel:${stand.phone_default}`}>
                  <Phone size={20} />
                  <span>{stand.phone_default}</span>
                </ContactItem>
              )}
            </ContactCard>

            <InfoSection />
          </Sidebar>
          
          <MainContent>
            <SectionTitle>{content.about} {stand.name}</SectionTitle>
            <Description>
              {language === 'es' 
                ? (stand.description_es || stand.description_en || stand.description || content.noDescription)
                : (stand.description_en || stand.description_es || stand.description || content.noDescription)
              }
            </Description>
            
            {posts.length > 0 && (
              <>
                <SectionTitle style={{ marginTop: '3rem' }}>{content.promotions}</SectionTitle>
                <PostsGrid>
                  {posts.map((post) => (
                    <ProductCard 
                      key={post.id} 
                      post={{...post, isExpanded: expandedPost?.id === post.id}} 
                      onExpand={handleExpandPost} 
                    />
                  ))}
                </PostsGrid>
              </>
            )}
          </MainContent>
        </ContentWrapper>
        <ChatbotWidget slug={slug} />
      </PageWrapper>
    </>
  );
};

export default StandVirtualDetalle;