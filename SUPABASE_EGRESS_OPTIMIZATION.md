# Supabase Egress Usage Optimization Guide

## 🚨 Current Situation
- **Free Plan Limit**: 5 GB
- **Current Usage**: 101.86 GB (20x overage!)
- **Overage**: 96.86 GB
- **Daily Peaks**: Up to 7.6 GB per day

## 📊 Root Cause Analysis

### 1. **Inefficient Database Queries**
```javascript
// ❌ BAD: Fetches ALL columns
.select('*')

// ✅ GOOD: Select only needed columns
.select(`
  id,
  title,
  title_es,
  title_en,
  description,
  start_date,
  image_url
`)
```

### 2. **No Query Optimization**
- No pagination limits
- No result caching
- Multiple redundant queries
- Large text fields being transferred

### 3. **Real-time Subscriptions**
- Auth state listeners running continuously
- Potential real-time subscriptions not properly managed

## 🔧 Immediate Optimizations Applied

### Events.jsx Optimizations ✅
1. **Selective Column Fetching**: Only fetch necessary columns
2. **Query Caching**: Implement in-memory cache for repeated queries
3. **Debounced Search**: Reduce API calls during typing
4. **Pagination**: Limit results to 20 per query
5. **Optimized Search**: Remove description from search (large text field)

## 🎯 Additional Optimizations Needed

### 1. **User Store Optimization**
```javascript
// Current problematic query in useUserStore.js
.select('*, stands(*)')  // ❌ Fetches everything

// Optimized version
.select(`
  id,
  email,
  role,
  created_at,
  stands(id, name, slug, description, logo_url)
`)
```

### 2. **Analytics Optimization**
```javascript
// In useAnalytics.js - optimize frequent calls
// Add caching and reduce frequency of analytics queries
```

### 3. **Storage Optimization**
- Compress images before upload
- Use appropriate image formats (WebP)
- Implement lazy loading for images

## 📈 Expected Impact

### Before Optimization:
- **Events Page**: ~2-5 MB per load (all columns + large text)
- **User Data**: ~1-3 MB per user fetch
- **Search**: ~3-8 MB per search (includes descriptions)

### After Optimization:
- **Events Page**: ~0.5-1 MB per load (selected columns only)
- **User Data**: ~0.2-0.5 MB per user fetch
- **Search**: ~0.3-0.8 MB per search (no descriptions)
- **Cache Hit**: 0 MB (instant load from cache)

## 🛠️ Implementation Checklist

### ✅ Completed
- [x] Events.jsx query optimization
- [x] Events.jsx caching implementation
- [x] Events.jsx debounced search
- [x] Events.jsx selective column fetching

### 🔄 In Progress
- [ ] User store optimization
- [ ] Analytics query optimization
- [ ] Storage image optimization
- [ ] Real-time subscription cleanup

### 📋 Next Steps
1. **Optimize useUserStore.js**
2. **Review and optimize analytics queries**
3. **Implement image compression**
4. **Add query result caching across the app**
5. **Monitor egress usage daily**

## 💰 Cost Impact

### Current Monthly Cost (estimated):
- **Free Plan**: $0
- **Overage Charges**: ~$96.86 (at $1/GB)
- **Total**: ~$96.86/month

### After Optimization (estimated):
- **Free Plan**: $0
- **Overage Charges**: ~$5-15/month
- **Savings**: ~$80-90/month

## 🔍 Monitoring Strategy

### Daily Checks:
1. Monitor Supabase dashboard for egress usage
2. Check for unusual spikes in data transfer
3. Review query logs for inefficient patterns

### Weekly Reviews:
1. Analyze which pages/components use most data
2. Optimize based on usage patterns
3. Update caching strategies

## 🚀 Advanced Optimizations

### 1. **Server-Side Caching**
```javascript
// Implement Redis or similar for server-side caching
// Cache frequently accessed data for 5-15 minutes
```

### 2. **CDN for Static Assets**
- Move images to CDN
- Use optimized image formats
- Implement lazy loading

### 3. **Database Indexing**
- Add indexes on frequently queried columns
- Optimize search queries with full-text search

### 4. **Real-time Optimization**
- Limit real-time subscriptions
- Use polling instead of continuous streams where appropriate
- Implement connection pooling

## 📞 Emergency Actions

If egress usage continues to spike:

1. **Immediate**: Upgrade to Pro plan ($25/month) for higher limits
2. **Short-term**: Implement aggressive caching
3. **Long-term**: Migrate to self-hosted database for high-volume data

## 📊 Success Metrics

Track these metrics to measure optimization success:

- **Daily Egress**: Target < 1 GB/day
- **Query Response Time**: Target < 200ms
- **Cache Hit Rate**: Target > 80%
- **Monthly Cost**: Target < $25/month

---

**Priority**: High - Immediate action required to prevent excessive charges
**Timeline**: Complete optimizations within 1 week
**Expected Savings**: $80-90/month 