import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useLanguageStore } from '../store/useLanguageStore';
import { setCanonical, setBreadcrumbs } from '../utils/seo';

const PageWrapper = styled.div`
  background-color: #ffffff;
  min-height: 100vh;
`;

const Container = styled.div`
  max-width: 1100px;
  margin: 0 auto;
  padding: 2rem 1rem 3rem 1rem;
  
  @media (max-width: 768px) {
    padding: 1.5rem 0.8rem 2.5rem 0.8rem;
  }
  
  @media (max-width: 480px) {
    padding: 1rem 0.5rem 2rem 0.5rem;
  }
`;

const Title = styled.h1`
  font-size: 2.8rem;
  font-weight: 900;
  color: #222;
  margin-bottom: 1.5rem;
  border-left: 8px solid #F16925;
  padding-left: 1rem;
  letter-spacing: 2px;
  @media (max-width: 600px) {
    font-size: 2rem;
    padding-left: 0.5rem;
    border-left-width: 4px;
  }
`;

const Grid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
  align-items: flex-start;
  
  @media (max-width: 900px) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  @media (max-width: 768px) {
    gap: 1.5rem;
  }
  
  @media (max-width: 480px) {
    gap: 1rem;
  }
`;

const ImageGallery = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: auto auto;
  gap: 0.8rem 1rem;
  
  @media (max-width: 900px) {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 0.7rem 0.8rem;
  }
  
  @media (max-width: 768px) {
    gap: 0.6rem;
  }
  
  @media (max-width: 600px) {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: 1.5rem;
    padding-bottom: 1rem;
  }
  
  @media (max-width: 480px) {
    gap: 1.2rem;
  }

  /* Style the div containers */
  > div {
    display: flex;
    flex-direction: column;
  }

  /* Make the full image span both columns */
  > img {
    grid-column: 1 / span 2;
    
    @media (max-width: 600px) {
      grid-column: 1 / span 1;
    }
  }
`;

const ImageLabel = styled.div`
  font-size: 0.9rem;
  font-weight: 700;
  color: #F16925;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-top: 0.5rem;
  margin-bottom: 0.3rem;
  text-align: center;
  
  @media (max-width: 600px) {
    font-size: 0.8rem;
    margin-top: 0.4rem;
    margin-bottom: 0.2rem;
  }
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
  
  @media (max-width: 768px) {
    padding: 1rem;
  }
  
  @media (max-width: 480px) {
    padding: 1rem;
    align-items: center;
    justify-content: center;
  }
`;

const ModalContent = styled.div`
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  
  @media (max-width: 768px) {
    max-width: 95vw;
    max-height: 85vh;
  }
  
  @media (max-width: 480px) {
    max-width: 90vw;
    max-height: 85vh;
    justify-content: center;
  }
`;

const ModalImage = styled.img`
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  
  @media (max-width: 768px) {
    max-height: 65vh;
    border-radius: 8px;
  }
  
  @media (max-width: 480px) {
    max-height: 55vh;
    border-radius: 6px;
  }
`;

const ModalCaption = styled.p`
  color: white;
  font-size: 1.2rem;
  margin-top: 1rem;
  text-align: center;
  font-weight: 500;
  padding: 0 1rem;
  
  @media (max-width: 768px) {
    font-size: 1rem;
    margin-top: 0.8rem;
    padding: 0 0.5rem;
  }
  
  @media (max-width: 480px) {
    font-size: 0.9rem;
    margin-top: 0.6rem;
    padding: 0 0.25rem;
  }
`;

const CloseButton = styled.button`
  position: absolute;
  top: -50px;
  right: 0;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid white;
  color: white;
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
  }
  
  @media (max-width: 768px) {
    top: -55px;
    right: 5px;
    width: 44px;
    height: 44px;
    font-size: 1.6rem;
  }
  
  @media (max-width: 480px) {
    top: -60px;
    right: 0;
    width: 48px;
    height: 48px;
    font-size: 1.8rem;
    background: rgba(255, 255, 255, 0.3);
  }
`;

const NavigationButton = styled.button`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid white;
  color: white;
  font-size: 1.5rem;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  
  ${props => props.direction === 'left' ? 'left: -70px;' : 'right: -70px;'}
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-50%) scale(1.1);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  @media (max-width: 768px) {
    ${props => props.direction === 'left' ? 'left: -60px;' : 'right: -60px;'}
    width: 44px;
    height: 44px;
    font-size: 1.3rem;
  }
  
  @media (max-width: 480px) {
    ${props => props.direction === 'left' ? 'left: 5px;' : 'right: 5px;'}
    width: 48px;
    height: 48px;
    font-size: 1.4rem;
    background: rgba(255, 255, 255, 0.3);
    top: 50%;
    transform: translateY(-50%);
    
    &:hover {
      transform: translateY(-50%) scale(1.05);
    }
  }
`;

const GalleryImg = styled.img`
  width: 100%;
  height: 160px;
  object-fit: cover;
  border-radius: 10px;
  cursor: pointer;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.02);
  }
  
  @media (max-width: 900px) {
    height: 150px;
  }
  
  @media (max-width: 768px) {
    border-radius: 8px;
    height: 140px;
    
    &:hover {
      transform: scale(1.01);
    }
  }
  
  @media (max-width: 600px) {
    height: 200px;
  }
  
  @media (max-width: 480px) {
    height: 180px;
  }
`;

const GalleryImgFull = styled.img`
  width: 100%;
  height: auto;
  max-height: 320px;
  object-fit: contain;
  border-radius: 10px;
  grid-column: 1 / span 2;
  cursor: pointer;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.01);
  }
  
  @media (max-width: 768px) {
    border-radius: 8px;
    max-height: 280px;
  }
  
  @media (max-width: 600px) {
    grid-column: 1 / span 1;
    max-height: 240px;
  }
  
  @media (max-width: 480px) {
    max-height: 200px;
  }
`;

const TextSection = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  
  @media (max-width: 900px) {
    padding: 1rem 0.5rem 0 0.5rem;
  }
  
  @media (max-width: 768px) {
    padding: 0.8rem 0.3rem 0 0.3rem;
  }
  
  @media (max-width: 600px) {
    padding: 1rem 0.2rem 0 0.2rem;
  }
  
  @media (max-width: 480px) {
    padding: 0.5rem 0.1rem 0 0.1rem;
  }
`;

const Paragraph = styled.p`
  color: #444;
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 1.2rem;
  text-align: justify;
  
  @media (max-width: 768px) {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1rem;
    text-align: left;
  }
  
  @media (max-width: 600px) {
    font-size: 0.95rem;
    margin-bottom: 0.9rem;
  }
  
  @media (max-width: 480px) {
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 0.8rem;
  }
`;

const Highlight = styled.span`
  font-weight: bold;
  color: #F16925;
`;

const AboutUs = () => {
  const { language } = useLanguageStore();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  useEffect(() => {
    setCanonical('https://www.themercadodistrict.com/aboutus');
    setBreadcrumbs('https://www.themercadodistrict.com/aboutus', language === 'es' ? 'Sobre nosotros' : 'About us');
  }, [language]);

  const images = [
    {
      src: 'before.webp',
      caption: language === 'es' ? 'Mercado Food Hall - Antes' : 'Mercado Food Hall - Before',
      alt: 'Mercado Food Hall'
    },
    {
      src: 'after.webp',
      caption: language === 'es' ? 'Bargain Bazaar - Después' : 'Bargain Bazaar - After',
      alt: 'Bargain Bazaar'
    },
    {
      src: 'owners.webp',
      caption: language === 'es' ? 'Nuestros Fundadores' : 'Our Founders',
      alt: 'Owners'
    }
  ];

  const content = {
    title: language === 'es' ? 'ESTE ES MERCADO FOOD HALL' : 'THIS IS MERCADO FOOD HALL',
    paragraph1: language === 'es' 
      ? 'Una vez conocido como Bargain Bazaar, el único mercado de pulgas interior del Valle de Texas durante más de 35 años, este espacio enfrentó tiempos difíciles antes de ser adquirido por Jesús González en 2019. González, un visionario remodelador de edificios en dificultades, vio una oportunidad para reimaginar el mercado como un centro vibrante para el emprendimiento local y la cultura.'
      : 'Once known as Bargain Bazaar, the Texas Valley\'s only indoor flea market for over 35 years, this space faced tough times before being acquired by Jesús González in 2019. González, a visionary remodeler of distressed buildings, saw an opportunity to reimagine the market as a vibrant center for local entrepreneurship and culture.',
    paragraph2: language === 'es'
      ? 'Inspirado por mercados urbanos reconocidos alrededor del mundo, González transformó el edificio en dificultades en Mercado Food Hall. Reabierto en 2021, Mercado ahora ofrece más de 200 espacios para chefs, artesanos, profesionales de la belleza y marcas locales, todo dentro de una atmósfera enérgica y acogedora.'
      : 'Inspired by renowned urban markets around the world, González transformed the struggling building into Mercado Food Hall. Reopened in 2021, Mercado now offers over 200 spaces for chefs, artisans, beauty professionals, and local brands, all within an energetic and welcoming atmosphere.',
    paragraph3: language === 'es'
      ? 'Mercado Food Hall rápidamente se convirtió en más que solo un centro de negocios—es un lugar de reunión animado donde la cultura, la creatividad y la comunidad prosperan. Los visitantes disfrutan sabores de todo el mundo y descubren talento local único, mientras los emprendedores encuentran un espacio inspirador para lanzar sus sueños.'
      : 'Mercado Food Hall quickly became more than just a business hub—it\'s a lively gathering place where culture, creativity, and community thrive. Visitors enjoy flavors from around the globe and discover unique local talent, while entrepreneurs find an inspiring space to launch their dreams.',
    paragraph4: language === 'es'
      ? 'Al combinar tradición con innovación, Mercado Food Hall se erige como un símbolo de renovación para McAllen y el Valle del Río Grande—celebrando el espíritu local, forjando nuevas conexiones y creando oportunidades infinitas de crecimiento.'
      : 'By blending tradition with innovation, Mercado Food Hall stands as a symbol of renewal for McAllen and the Rio Grande Valley—celebrating local spirit, forging new connections, and creating endless opportunities for growth.'
  };

  const openModal = (imageIndex) => {
    setSelectedImageIndex(imageIndex);
    setIsModalOpen(true);
    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden';
  };

  const closeModal = React.useCallback(() => {
    setIsModalOpen(false);
    setSelectedImageIndex(0);
    // Restore body scroll
    document.body.style.overflow = 'unset';
  }, []);

  const nextImage = React.useCallback(() => {
    setSelectedImageIndex((prev) => (prev + 1) % images.length);
  }, [images.length]);

  const prevImage = React.useCallback(() => {
    setSelectedImageIndex((prev) => (prev - 1 + images.length) % images.length);
  }, [images.length]);

  // Handle keyboard navigation
  React.useEffect(() => {
    const handleKeyPress = (e) => {
      if (isModalOpen) {
        if (e.key === 'Escape') {
          closeModal();
        } else if (e.key === 'ArrowRight') {
          nextImage();
        } else if (e.key === 'ArrowLeft') {
          prevImage();
        }
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isModalOpen, nextImage, prevImage, closeModal]);

  // Handle touch/swipe gestures for mobile
  React.useEffect(() => {
    if (!isModalOpen) return;

    let startX = 0;
    let startY = 0;

    const handleTouchStart = (e) => {
      startX = e.touches[0].clientX;
      startY = e.touches[0].clientY;
    };

    const handleTouchEnd = (e) => {
      if (!startX || !startY) return;
      
      const endX = e.changedTouches[0].clientX;
      const endY = e.changedTouches[0].clientY;
      
      const diffX = startX - endX;
      const diffY = startY - endY;
      
      // Only handle horizontal swipes if they're significantly more than vertical
      if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
        if (diffX > 0) {
          nextImage(); // Swipe left = next image
        } else {
          prevImage(); // Swipe right = previous image
        }
      }
      
      startX = 0;
      startY = 0;
    };

    const modalElement = document.querySelector('[data-modal]');
    if (modalElement) {
      modalElement.addEventListener('touchstart', handleTouchStart, { passive: true });
      modalElement.addEventListener('touchend', handleTouchEnd, { passive: true });
      
      return () => {
        modalElement.removeEventListener('touchstart', handleTouchStart);
        modalElement.removeEventListener('touchend', handleTouchEnd);
      };
    }
  }, [isModalOpen, nextImage, prevImage]);

  return (
    <>
      <PageWrapper>
        <Container>
          <Title>{content.title}</Title>
          <Grid>
            <ImageGallery>
              <div>
                <GalleryImg 
                  src={images[0].src} 
                  alt={images[0].alt} 
                  loading="lazy" 
                  onClick={() => openModal(0)} 
                />
                <ImageLabel>{language === 'es' ? 'Antes' : 'Before'}</ImageLabel>
              </div>
              <div>
                <GalleryImg 
                  src={images[1].src} 
                  alt={images[1].alt} 
                  loading="eager" 
                  fetchpriority="high" 
                  onClick={() => openModal(1)} 
                />
                <ImageLabel>{language === 'es' ? 'Después' : 'After'}</ImageLabel>
              </div>
              <GalleryImgFull 
                src={images[2].src} 
                alt={images[2].alt} 
                loading="lazy" 
                onClick={() => openModal(2)} 
              />
            </ImageGallery>
            <TextSection>
              <Paragraph>
                {content.paragraph1}
              </Paragraph>
              <Paragraph>
                {content.paragraph2}
              </Paragraph>
              <Paragraph>
                {content.paragraph3}
              </Paragraph>
              <Paragraph>
                {content.paragraph4}
              </Paragraph>
            </TextSection>
          </Grid>
        </Container>
      </PageWrapper>
      {isModalOpen && (
        <Modal onClick={closeModal} data-modal>
          <ModalContent onClick={(e) => e.stopPropagation()}>
            <CloseButton onClick={closeModal}>×</CloseButton>
            <NavigationButton 
              direction="left" 
              onClick={prevImage}
              disabled={images.length <= 1}
            >
              ←
            </NavigationButton>
            <NavigationButton 
              direction="right" 
              onClick={nextImage}
              disabled={images.length <= 1}
            >
              →
            </NavigationButton>
            <ModalImage 
              src={images[selectedImageIndex].src} 
              alt={images[selectedImageIndex].alt} 
            />
            <ModalCaption>
              {images[selectedImageIndex].caption}
            </ModalCaption>
          </ModalContent>
        </Modal>
      )}
    </>
  );
};

export default AboutUs; 