import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { supabase } from '../utils/supabaseClient';
import { ArrowLeft, Calendar, Clock, MapPin, User, ExternalLink } from 'lucide-react';
import { useLanguageStore } from '../store/useLanguageStore';
import Loader from '../components/Loader';

const PageWrapper = styled.div`
  background-color: #f4f4f9;
  min-height: 100vh;
`;

const BannerContainer = styled.div`
  position: relative;
  width: 100%;
  height: 400px;
  overflow: hidden;

  @media (max-width: 768px) {
    height: 300px;
  }
`;

const BannerImg = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  transition: opacity 0.3s ease;
  
  &.loading {
    opacity: 0;
  }
  
  &.loaded {
    opacity: 1;
  }
`;

const BannerPlaceholder = styled.div`
  width: 100%;
  height: 100%;
  background: #eaeaea;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: #888;
`;

const BannerOverlay = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.9), transparent);
  padding: 2rem;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const TopBannerOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 1rem 2rem;
  z-index: 10;

  @media (max-width: 768px) {
    padding: 0.5rem 1rem;
  }
`;

const EventTitle = styled.h1`
  margin: 0;
  font-size: 3rem;
  font-weight: 700;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const CategoryBadge = styled.span`
  background-color: #F16925;
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-top: 1rem;

  @media (max-width: 768px) {
    font-size: 0.8rem;
    padding: 0.2rem 0.6rem;
  }
`;

const ContentWrapper = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    padding: 1rem;
    gap: 1rem;
  }
`;

const MainContent = styled.div`
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  @media (max-width: 768px) {
    padding: 1rem;
  }
`;

const Sidebar = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const InfoCard = styled.div`
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
`;

const InfoItem = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  color: #333;

  &:last-child {
    margin-bottom: 0;
  }

  svg {
    color: #F16925;
  }
`;

const Description = styled.div`
  line-height: 1.7;
  color: #666;
  white-space: pre-wrap;
  margin-top: 1.5rem;
`;

const BackButton = styled(Link)`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.2rem;
  background-color: #F16925;
  color: white;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    background: #e05a1a;
    transform: translateY(-2px);
  }
`;

const ExternalLinkButton = styled.a`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: #F16925;
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  margin-top: 1rem;
  transition: all 0.3s ease;

  &:hover {
    background: #e05a1a;
    transform: translateY(-2px);
  }
`;

const EventDetails = () => {
  const { id } = useParams();
  const [event, setEvent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [imageLoaded, setImageLoaded] = useState(false);
  const { language } = useLanguageStore();

  const content = {
    backToEvents: language === 'es' ? 'Regresar a Eventos' : 'Back to Events',
    noImage: language === 'es' ? 'Sin imagen disponible' : 'No image available',
    date: language === 'es' ? 'Fecha' : 'Date',
    schedule: language === 'es' ? 'Horario' : 'Schedule',
    location: language === 'es' ? 'Ubicación' : 'Location',
    organizer: language === 'es' ? 'Organizador' : 'Organizer',
    moreInfo: language === 'es' ? 'Ver más información' : 'View more information',
    error: language === 'es' ? 'No se pudo cargar la información del evento' : 'Could not load event information',
    notFound: language === 'es' ? 'Evento no encontrado' : 'Event not found',
    categories: {
      'Food': language === 'es' ? 'Comida' : 'Food',
      'Entertainment': language === 'es' ? 'Entretenimiento' : 'Entertainment',
      'Music': language === 'es' ? 'Música' : 'Music',
      'Art': language === 'es' ? 'Arte' : 'Art',
      'Sports': language === 'es' ? 'Deportes' : 'Sports',
      'Business': language === 'es' ? 'Negocios' : 'Business',
      'Other': language === 'es' ? 'Otros' : 'Other'
    }
  };

  // Helper function to get the appropriate text based on current language
  const getLocalizedText = (field) => {
    if (!event) return '';
    
    const esField = `${field}_es`;
    const enField = `${field}_en`;
    const fallbackField = field;

    if (language === 'es') {
      return event[esField] || event[enField] || event[fallbackField] || '';
    } else {
      return event[enField] || event[esField] || event[fallbackField] || '';
    }
  };

  useEffect(() => {
    const fetchEventDetails = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('events')
          .select('*')
          .eq('id', id)
          .single();

        if (error) throw error;
        setEvent(data);
      } catch (error) {
        console.error('Error fetching event:', error);
        setError(content.error);
      } finally {
        setLoading(false);
      }
    };

    fetchEventDetails();
  }, [id, content.error]);

  // Función para traducir la categoría
  const translateCategory = () => {
    const category = getLocalizedText('category');
    return content.categories[category] || category;
  };

  if (loading) return <Loader />;

  if (error || !event) {
    return (
      <>
        <PageWrapper>
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh', flexDirection: 'column', gap: '1rem' }}>
            <p style={{ color: '#ff4444' }}>{error || content.notFound}</p>
            <BackButton to="/events">
              <ArrowLeft size={18} />
              {content.backToEvents}
            </BackButton>
          </div>
        </PageWrapper>
      </>
    );
  }

  const bannerUrl = event.banner_url || event.image_url;
  const eventTitle = getLocalizedText('title');
  const eventDescription = getLocalizedText('description');
  const eventLocation = getLocalizedText('location');
  const eventOrganizer = getLocalizedText('organizer');

  return (
    <>
      <PageWrapper>
        <BannerContainer>
          {bannerUrl ? (
            <BannerImg 
              src={bannerUrl} 
              alt={eventTitle}
              loading="eager"
              fetchpriority="high"
              className={imageLoaded ? 'loaded' : 'loading'}
              onLoad={() => setImageLoaded(true)}
            />
          ) : (
            <BannerPlaceholder>
              {content.noImage}
            </BannerPlaceholder>
          )}
          <TopBannerOverlay>
            <BackButton to="/events">
              <ArrowLeft size={18} />
              {content.backToEvents}
            </BackButton>
          </TopBannerOverlay>
          <BannerOverlay>
            <EventTitle>{eventTitle}</EventTitle>
            {event.category && <CategoryBadge>{translateCategory()}</CategoryBadge>}
          </BannerOverlay>
        </BannerContainer>

        <ContentWrapper>
          <MainContent>
            <Description>{eventDescription}</Description>
          </MainContent>

          <Sidebar>
            <InfoCard>
              <InfoItem>
                <Calendar size={20} />
                <div>
                  <strong>{content.date}:</strong>
                  <div>
                    {event.start_date} {event.end_date && `- ${event.end_date}`}
                  </div>
                </div>
              </InfoItem>

              <InfoItem>
                <Clock size={20} />
                <div>
                  <strong>{content.schedule}:</strong>
                  <div>
                    {event.start_time} {event.end_time && `- ${event.end_time}`}
                  </div>
                </div>
              </InfoItem>

              <InfoItem>
                <MapPin size={20} />
                <div>
                  <strong>{content.location}:</strong>
                  <div>{eventLocation}</div>
                </div>
              </InfoItem>

              {eventOrganizer && (
                <InfoItem>
                  <User size={20} />
                  <div>
                    <strong>{content.organizer}:</strong>
                    <div>{eventOrganizer}</div>
                  </div>
                </InfoItem>
              )}

              {event.link && (
                <ExternalLinkButton 
                  href={event.link} 
                  target="_blank" 
                  rel="noopener noreferrer"
                >
                  <ExternalLink size={18} />
                  {content.moreInfo}
                </ExternalLinkButton>
              )}
            </InfoCard>
          </Sidebar>
        </ContentWrapper>
      </PageWrapper>
    </>
  );
};

export default EventDetails; 