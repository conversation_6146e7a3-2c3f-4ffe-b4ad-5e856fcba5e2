import React from 'react';
import { createBrowserRouter } from 'react-router-dom';
import Layout from '../components/Layout';
import Home from '../pages/Home';
import AboutUs from '../pages/AboutUs';
import Events from '../pages/Events';
import EventDetails from '../pages/EventDetails';
import Login from '../pages/Login';
import Register from '../pages/Register';
import ForgotPassword from '../pages/ForgotPassword';
import ResetPassword from '../pages/ResetPassword';
import VerifyEmail from '../pages/VerifyEmail';
import MyPanel from '../pages/MyPanel';
import AdminPanel from '../pages/AdminPanel';
import StandsVirtuales from '../pages/StandsVirtuales';
import StandVirtualDetalle from '../pages/StandVirtualDetalle';
import TenantDetail from '../pages/TenantDetail';
import ReproducirVideo from '../pages/ReproducirVideo';
import Hours from '../pages/Hours';
import ProtectedRoute from '../components/ProtectedRoute';

const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        index: true,
        element: <Home />
      },
      {
        path: 'about',
        element: <AboutUs />
      },
      {
        path: 'events',
        element: <Events />
      },
      {
        path: 'eventinfo/:id',
        element: <EventDetails />
      },
      {
        path: 'login',
        element: <Login />
      },
      {
        path: 'register',
        element: <Register />
      },
      {
        path: 'forgot-password',
        element: <ForgotPassword />
      },
      {
        path: 'reset-password',
        element: <ResetPassword />
      },
      {
        path: 'verify-email',
        element: <VerifyEmail />
      },
      {
        path: 'dashboard',
        element: <ProtectedRoute><MyPanel /></ProtectedRoute>
      },
      {
        path: 'admin',
        element: <AdminPanel />
      },
      {
        path: 'admin/events',
        element: <AdminPanel />
      },
      {
        path: 'admin/users',
        element: <AdminPanel />
      },
      {
        path: 'admin/tenants',
        element: <AdminPanel />
      },
      {
        path: 'stands-virtuales',
        element: <StandsVirtuales />
      },
      {
        path: 'stands-virtuales/:slug',
        element: <StandVirtualDetalle />
      },
      {
        path: 'tenant/:slug',
        element: <TenantDetail />
      },
      {
        path: 'video',
        element: <ReproducirVideo />
      },
      {
        path: 'hours',
        element: <Hours />
      }
    ]
  }
]);

export default router; 