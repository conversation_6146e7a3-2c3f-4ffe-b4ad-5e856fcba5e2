-- 20250708120000_expire_events_trigger.sql
-- Purpose: Automatically mark events as 'expired' once their date/time has passed so the front-end can easily hide them.
-- This file adheres to the project SQL style guide and is idempotent.

-- 1. Add status column to events table --------------------------------------------------
ALTER TABLE public.events 
ADD COLUMN IF NOT EXISTS status TEXT NOT NULL DEFAULT 'active';

-- 2. Function: update_event_status -------------------------------------------------------
CREATE OR REPLACE FUNCTION public.update_event_status()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = ''
AS $$
BEGIN
  -- Combine date + time components to decide if the event is already expired.
  -- Order of precedence: end_date + end_time > end_date > start_date + start_time > start_date.
  -- If the chosen timestamp is in the past, mark as expired; otherwise keep active.

  DECLARE
    event_datetime TIMESTAMP;
  BEGIN
    IF NEW.end_date IS NOT NULL THEN
      IF NEW.end_time IS NOT NULL THEN
        event_datetime := (NEW.end_date::timestamp + NEW.end_time::time);
      ELSE
        event_datetime := (NEW.end_date::timestamp + time '23:59:59');
      END IF;
    ELSIF NEW.start_date IS NOT NULL THEN
      IF NEW.start_time IS NOT NULL THEN
        event_datetime := (NEW.start_date::timestamp + NEW.start_time::time);
      ELSE
        event_datetime := (NEW.start_date::timestamp + time '23:59:59');
      END IF;
    ELSE
      -- No date provided: keep status as is
      RETURN NEW;
    END IF;

    IF now() >= event_datetime THEN
      NEW.status := 'expired';
    ELSE
      NEW.status := 'active';
    END IF;

    RETURN NEW;
  END;
END;
$$;

-- 3. Trigger: apply function before insert/update ---------------------------------------
DROP TRIGGER IF EXISTS trg_update_event_status ON public.events;

CREATE TRIGGER trg_update_event_status
BEFORE INSERT OR UPDATE ON public.events
FOR EACH ROW EXECUTE FUNCTION public.update_event_status(); 