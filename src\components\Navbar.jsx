import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useAuthStore } from '../store/useAuthStore';
import { useUserStore } from '../store/useUserStore';
import { ChevronDown, LayoutDashboard, Menu, X, LogOut } from 'lucide-react';
import { useNavigate, Link } from 'react-router-dom';
import { useLanguageStore } from '../store/useLanguageStore';
import { FaUserCircle } from 'react-icons/fa';
import { FaFacebookF, FaInstagram, FaTiktok, FaChevronDown, FaGoogle } from 'react-icons/fa';
import { Mail } from 'lucide-react';

const UserDropdown = styled.div`
  position: relative;
  border: 2px solid #ffffff;
  padding: clamp(0.3rem, 0.8vw, 0.5rem) clamp(1rem, 2.5vw, 2rem);
  background-color: #222222;
  border-radius: 100px;
  z-index: 10000;
  flex-shrink: 0;
  min-width: fit-content;
  max-width: 250px;

  @media (max-width: 1200px) {
    padding: clamp(0.3rem, 0.6vw, 0.4rem) clamp(0.8rem, 2vw, 1.5rem);
    max-width: 200px;
  }

  @media (max-width: 900px) {
    padding: 0.4rem 1rem;
    max-width: 180px;
  }

  @media (max-width: 1024px) {
    max-width: none;
    width: 100%;
    margin-top: 0.5rem;
  }
`;

const DropdownButton = styled.button`
  background: none;
  border: none;
  color: white;
  font-size: clamp(14px, 1.8vw, 16px);
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: clamp(0.2rem, 0.5vw, 0.25rem);
  width: 100%;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: color 0.2s ease;

  &:hover {
    color: #ffe3d1;
  }

  svg {
    transition: transform 0.3s;
    transform: ${props => props.$isOpen ? 'rotate(180deg)' : 'rotate(0deg)'};
    flex-shrink: 0;
  }

  @media (max-width: 1200px) {
    font-size: clamp(12px, 1.6vw, 14px);
  }

  @media (max-width: 1024px) {
    white-space: normal;
    text-overflow: unset;
    overflow: visible;
    justify-content: center;
    text-align: center;
  }
`;

const DropdownMenu = styled.div`
  position: absolute;
  top: 120%;
  right: 0;
  background: #222;
  padding: 1rem 1rem;
  border-radius: 6px;
  border: 1px solid #ffffff;
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  z-index: 10001;
  display: flex;
  width: 200px;
  flex-direction: column;
  gap: 1.5vh;
  align-items: flex-start;
  min-width: 200px;

  button, a {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 14px;
    cursor: pointer;
    padding: 0.5rem 0;
    width: 100%;
    text-align: left;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;

    &:hover {
      color: #f16925;
      padding-left: 0.5rem;
    }

    svg {
      flex-shrink: 0;
    }

    &.dashboard-link {
      color: white;
      
      &:hover {
        color: #f16925;
        padding-left: 0.5rem;
      }
    }
    
    &.admin-link {
      background: #f16925;
      color: white;
      text-align: center;
      justify-content: center;
      
      &:hover {
        background: #e05a1a;
        color: white;
        padding-left: 0.5rem;
      }
    }
  }
`;

const Nav = styled.nav`
  display: grid;
  grid-template-columns: auto 1fr;
  align-items: center;
  padding: .5rem 2rem;
  background: #f16925;
  color: white;
  position: relative;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  min-height: 70px;
  overflow-x: auto;
  overflow-y: hidden;

  /* Ensure content doesn't break layout */
  @supports not (display: grid) {
    display: flex;
    justify-content: space-between;
  }

  @media (max-width: 1200px) {
    padding: .5rem 1.5rem;
  }

  @media (max-width: 900px) {
    padding: .5rem 1rem;
    grid-template-columns: auto auto;
    justify-content: space-between;
  }

  @media (max-width: 600px) {
    padding: .5rem 0.75rem;
  }
`;



const LogoContainer = styled.div`
  display: flex;
  align-items: center;
  gap: clamp(0.5rem, 1.5vw, 1rem);
  flex-shrink: 0;
  min-width: fit-content;

  @media (max-width: 900px) {
    gap: 0.5rem;
  }
`;

const Logo = styled.img`
  width: clamp(120px, 15vw, 150px);
  height: auto;
  flex-shrink: 0;

  @media (max-width: 900px) {
    width: clamp(100px, 12vw, 130px);
  }

  @media (max-width: 600px) {
    width: clamp(90px, 10vw, 120px);
  }
`;

const FoodieFestLogo = styled.img`
  width: clamp(70px, 8vw, 90px);
  height: auto;
  padding: 0 clamp(2px, 0.5vw, 5px);
  flex-shrink: 0;

  @media (max-width: 900px) {
    width: clamp(60px, 7vw, 80px);
  }

  @media (max-width: 600px) {
    width: clamp(50px, 6vw, 70px);
  }
`;

const HamburgerButton = styled.button`
  display: none;
  background: none;
  border: 2px solid transparent;
  color: white;
  cursor: pointer;
  padding: clamp(0.3rem, 0.8vw, 0.5rem);
  border-radius: 6px;
  transition: all 0.2s ease;
  justify-self: end;

  &:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.3);
  }

  @media (max-width: 1024px) {
    display: block;
  }
`;

const StyledNavLink = styled(Link)`
  color: white;
  text-decoration: none;
  font-size: clamp(16px, 2.2vw, 20px);
  padding: 0.5rem clamp(0.3rem, 1vw, 0.8rem);
  font-weight: 700;
  transition: color 0.2s, transform 0.2s;
  white-space: nowrap;
  flex-shrink: 0;
  border-radius: 4px;

  &:hover {
    color: #ffe3d1;
    transform: translateY(-1px);
  }

  @media (max-width: 1200px) {
    font-size: clamp(14px, 2vw, 18px);
    padding: 0.4rem clamp(0.2rem, 0.8vw, 0.6rem);
  }

  @media (max-width: 1024px) {
    width: 100%;
    text-align: center;
    padding: 1rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    font-size: 18px;
    white-space: normal;
    flex-shrink: 1;
  }
`;

const StyledButton = styled.button`
  background: transparent;
  color: #fff;
  border: 2px solid #fff;
  border-radius: 8px;
  padding: clamp(0.4rem, 1vw, 0.7rem) clamp(0.8rem, 2vw, 1.2rem);
  font-size: clamp(14px, 1.8vw, 18px);
  font-weight: bold;
  margin-left: clamp(0.2rem, 0.8vw, 0.5rem);
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: none;
  display: flex;
  align-items: center;
  gap: clamp(0.3rem, 0.8vw, 0.5rem);
  white-space: nowrap;
  flex-shrink: 0;
  min-width: fit-content;
  max-width: 250px;

  /* Handle text overflow for very long text */
  overflow: hidden;
  text-overflow: ellipsis;

  &:hover {
    background: rgba(255,255,255,0.1);
    color: #fff;
    border-color: #fff;
    transform: translateY(-1px);
  }

  @media (max-width: 1200px) {
    padding: clamp(0.3rem, 0.8vw, 0.6rem) clamp(0.6rem, 1.5vw, 1rem);
    font-size: clamp(12px, 1.6vw, 16px);
    border-width: 2px;
  }

  @media (max-width: 900px) {
    padding: 0.5rem 0.8rem;
    font-size: 14px;
    margin-left: 0.3rem;
    max-width: 200px;
  }

  @media (max-width: 1024px) {
    width: 100%;
    max-width: none;
    justify-content: center;
    margin-left: 0;
    margin-top: 0.5rem;
    white-space: normal;
    text-align: center;
    overflow: visible;
    text-overflow: unset;
  }
`;



const LanguageButton = styled.button`
  background: none;
  border: 2px solid transparent;
  color: white;
  font-size: clamp(18px, 2vw, 22px);
  font-weight: bold;
  cursor: pointer;
  margin-left: clamp(0.5rem, 1vw, 1rem);
  display: flex;
  align-items: center;
  padding: clamp(0.2rem, 0.5vw, 0.4rem) clamp(0.4rem, 0.8vw, 0.6rem);
  border-radius: 6px;
  transition: all 0.2s ease;
  flex-shrink: 0;
  min-width: 40px;

  &:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.3);
    transform: translateY(-1px);
  }

  @media (max-width: 1200px) {
    font-size: clamp(16px, 1.8vw, 20px);
    margin-left: clamp(0.3rem, 0.8vw, 0.8rem);
  }

  @media (max-width: 900px) {
    font-size: 18px;
    margin-left: 0.5rem;
    padding: 0.3rem 0.5rem;
  }

  @media (max-width: 1024px) {
    margin-left: 0;
    margin-top: 0.5rem;
  }
`;

const SocialIcons = styled.div`
  display: flex;
  align-items: center;
  gap: clamp(0.6rem, 1.2vw, 1.2rem);
  margin-left: clamp(0.8rem, 1.5vw, 1.5rem);
  flex-shrink: 0;

  a {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.3rem;
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255,255,255,0.1);
      transform: translateY(-1px);
    }

    svg {
      width: clamp(18px, 2vw, 22px);
      height: clamp(18px, 2vw, 22px);
    }
  }

  @media (max-width: 1200px) {
    gap: clamp(0.5rem, 1vw, 1rem);
    margin-left: clamp(0.6rem, 1.2vw, 1.2rem);
  }

  @media (max-width: 900px) {
    gap: 0.8rem;
    margin-left: 1rem;

    a svg {
      width: 20px;
      height: 20px;
    }
  }

  @media (max-width: 1024px) {
    margin-left: 0;
    margin-top: 1rem;
    justify-content: center;
    width: 100%;
    gap: 1.5rem;
  }
`;

const MoreButton = styled.button`
  background: none;
  border: 2px solid transparent;
  color: #fff;
  font-size: clamp(16px, 2vw, 20px);
  cursor: pointer;
  margin-left: clamp(0.5rem, 1vw, 1rem);
  display: flex;
  align-items: center;
  gap: clamp(0.3rem, 0.6vw, 0.4rem);
  font-weight: 700;
  position: relative;
  padding: clamp(0.3rem, 0.5vw, 0.4rem) clamp(0.4rem, 0.8vw, 0.6rem);
  border-radius: 6px;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex-shrink: 0;

  &:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.3);
    transform: translateY(-1px);
  }

  @media (max-width: 1200px) {
    font-size: clamp(14px, 1.8vw, 18px);
    margin-left: clamp(0.3rem, 0.8vw, 0.8rem);
  }

  @media (max-width: 1024px) {
    display: none;
  }
`;

const MoreMenu = styled.div`
  position: absolute;
  top: calc(100% + 10px);
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.15);
  padding: clamp(1rem, 2vw, 1.5rem) clamp(1.2rem, 2.5vw, 2rem);
  z-index: 3000;
  min-width: clamp(180px, 25vw, 220px);
  max-width: 300px;
  display: flex;
  flex-direction: column;
  gap: clamp(0.8rem, 1.5vw, 1.2rem);
  border: 1px solid rgba(0,0,0,0.05);
  white-space: nowrap;

  &::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid #fff;
  }

  @media (max-width: 1400px) {
    left: auto;
    right: -20px;
    transform: none;

    &::before {
      left: auto;
      right: 40px;
      transform: none;
    }
  }

  @media (max-width: 1200px) {
    right: -10px;
    min-width: 160px;

    &::before {
      right: 30px;
    }
  }

  @media (max-width: 600px) {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    transform: none;
    min-width: unset;
    max-width: none;
    width: 100vw;
    border-radius: 0 0 12px 12px;
    padding: 1.2rem 1rem;
    align-items: center;
    white-space: normal;

    &::before {
      display: none;
    }
  }
`;

const MoreMenuLink = styled(Link)`
  color: #f16925;
  text-decoration: none;
  font-size: clamp(0.9rem, 1.5vw, 1.1rem);
  font-weight: 600;
  padding: clamp(0.4rem, 0.8vw, 0.6rem) 0;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &:hover {
    background: rgba(241, 105, 37, 0.08);
    color: #e05a1a;
    padding-left: clamp(0.3rem, 0.8vw, 0.5rem);
    text-decoration: none;
    transform: translateX(2px);
  }

  @media (max-width: 600px) {
    white-space: normal;
    overflow: visible;
    text-overflow: unset;
    text-align: center;
  }
`;

const MoreContainer = styled.div`
  position: relative;
  display: inline-block;
  
  @media (max-width: 1024px) {
    display: none;
  }
`;

const MobileOnly = styled.div`
  display: none;
  @media (max-width: 1024px) {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    gap: 1rem;
  }
`;

const NavLinks = styled.div`
  display: flex;
  gap: clamp(0.8rem, 2vw, 2rem);
  align-items: center;
  justify-self: end;
  flex-wrap: nowrap;
  min-width: 0;
  overflow-x: auto;
  overflow-y: hidden;

  /* Smooth scrolling for overflow */
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;

  /* Hide scrollbar but keep functionality */
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }

  @media (max-width: 1200px) {
    gap: clamp(0.6rem, 1.5vw, 1.5rem);
  }

  @media (max-width: 900px) {
    gap: clamp(0.5rem, 1vw, 1rem);
  }

  @media (max-width: 1024px) {
    display: ${props => props.$isOpen ? 'flex' : 'none'};
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #111;
    flex-direction: column;
    padding: 1rem;
    gap: 1rem;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    align-items: stretch;
    overflow-x: visible;
    overflow-y: auto;
  }
`;

export const Navbar = () => {
  const user = useAuthStore((state) => state.user);
  const role = useUserStore((state) => state.role);
  const userData = useUserStore((state) => state.userData);
  const fetchUserData = useUserStore((state) => state.fetchUserData);
  const logout = useAuthStore((state) => state.logout);
  const [open, setOpen] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showMore, setShowMore] = useState(false);
  const navigate = useNavigate();
  const { language, toggleLanguage } = useLanguageStore();

  useEffect(() => {
    if (user?.id) {
      fetchUserData(user.id);
    }
  }, [user, fetchUserData]);

  useEffect(() => {
    if (!showMore) return;
    const handleClick = (e) => {
      if (!e.target.closest('.more-container')) {
        setShowMore(false);
      }
    };
    document.addEventListener('mousedown', handleClick);
    return () => document.removeEventListener('mousedown', handleClick);
  }, [showMore]);

  useEffect(() => {
    if (!open) return;
    const handleClick = (e) => {
      if (!e.target.closest('.user-dropdown')) {
        setOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClick);
    return () => document.removeEventListener('mousedown', handleClick);
  }, [open]);



  const handleDashboardClick = () => {
    navigate('/dashboard');
    setOpen(false);
    setIsMenuOpen(false);
  };

  const handleLogout = async () => {
    await logout();
    setOpen(false);
    setIsMenuOpen(false);
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <Nav>
      <LogoContainer>
        <a href="/">
          <Logo src="/logo.webp" alt="logo eme" loading="eager" fetchPriority="high" />
        </a>
        <FoodieFestLogo src="/Foodie fest website logo.webp" alt="Foodie Fest Logo" loading="eager" />
      </LogoContainer>
      <HamburgerButton
        onClick={toggleMenu}
        aria-label={
          isMenuOpen
            ? language === 'es'
              ? 'Cerrar menú de navegación'
              : 'Close navigation menu'
            : language === 'es'
            ? 'Abrir menú de navegación'
            : 'Open navigation menu'
        }
        aria-expanded={isMenuOpen}
      >
        {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
      </HamburgerButton>
      <NavLinks $isOpen={isMenuOpen}>
        <StyledNavLink to="/stands-virtuales?category=Restaurant">
          {language === 'es' ? 'Comer' : 'Eat'}
        </StyledNavLink>
        <StyledNavLink to="/stands-virtuales?category=Retail">
          {language === 'es' ? 'Tiendas' : 'Shops'}
        </StyledNavLink>
        <StyledNavLink to="/stands-virtuales?category=Beauty">
          {language === 'es' ? 'Belleza' : 'Beauty'}
        </StyledNavLink>
        <MoreContainer className="more-container">
          <MoreButton className="more-btn" onClick={() => setShowMore((v) => !v)} title={language === 'es' ? 'Más opciones' : 'More options'}>
            {language === 'es' ? 'Más' : 'More'} <FaChevronDown style={{ transform: showMore ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'transform 0.2s ease' }} />
          </MoreButton>
          {showMore && (
            <MoreMenu className="more-menu">
              <MoreMenuLink to="/aboutus">{language === 'es' ? 'Sobre Nosotros' : 'About Us'}</MoreMenuLink>
              <MoreMenuLink to="/map-plant1">{language === 'es' ? 'Mapas' : 'Maps'}</MoreMenuLink>
              <MoreMenuLink to="/stands-virtuales">{language === 'es' ? 'Explorar' : 'Explore'}</MoreMenuLink>
              <MoreMenuLink to="/events">{language === 'es' ? 'Eventos' : 'Events'}</MoreMenuLink>
              <MoreMenuLink to="/hours">{language === 'es' ? 'Horarios' : 'Hours'}</MoreMenuLink>
            </MoreMenu>
          )}
        </MoreContainer>
        <MobileOnly>
          <StyledNavLink to="/aboutus">{language === 'es' ? 'Sobre Nosotros' : 'About Us'}</StyledNavLink>
          <StyledNavLink to="/map-plant1">{language === 'es' ? 'Mapas' : 'Maps'}</StyledNavLink>
          <StyledNavLink to="/stands-virtuales">{language === 'es' ? 'Explorar' : 'Explore'}</StyledNavLink>
          <StyledNavLink to="/events">{language === 'es' ? 'Eventos' : 'Events'}</StyledNavLink>
          <StyledNavLink to="/hours">{language === 'es' ? 'Horarios' : 'Hours'}</StyledNavLink>
        </MobileOnly>
        <StyledButton onClick={() => window.location.href = '/#contact'}>
          <Mail style={{marginRight: 8, fontSize: 22}} />
          {language === 'es' ? 'Contacto' : 'Contact'}
        </StyledButton>
        
        {user ? (
          <UserDropdown className="user-dropdown" $isOpen={open}>
            <DropdownButton onClick={() => setOpen(!open)} $isOpen={open}>
              {userData?.name && userData?.lastname ? `${userData.name} ${userData.lastname}` : userData?.name || user.email} <ChevronDown />
            </DropdownButton>
            {open && (
              <DropdownMenu>
                {role === 'local' && (
                  <button onClick={handleDashboardClick}>
                    <LayoutDashboard size={16} />
                    Dashboard
                  </button>
                )}
                {userData?.role === 'admin' && userData?.is_admin && (
                  <button
                    onClick={() => {
                      console.log('🚀 Admin Panel clicked! Navigating to /admin');
                      navigate('/admin');
                      setOpen(false);
                    }}
                    className="admin-link"
                  >
                    Admin Panel
                  </button>
                )}
                <button
                  onClick={handleLogout}
                  className="text-white hover:text-blue-200 transition-colors"
                >
                  Cerrar Sesión
                </button>
              </DropdownMenu>
            )}
          </UserDropdown>
        ) : (
          <StyledButton onClick={() => window.location.href = '/login'}>
            <FaUserCircle style={{marginRight: 8, fontSize: 22}} />
            {language === 'es' ? 'Portal de Inquilinos' : 'Tenant Portal'}
          </StyledButton>
        )}
        
        <LanguageButton onClick={toggleLanguage} title={language === 'es' ? 'Cambiar a inglés' : 'Switch to Spanish'}>
          {language === 'es' ? 'EN' : 'ES'}
        </LanguageButton>
        <SocialIcons>
          <a href="https://g.co/kgs/9bdPKQE" target="_blank" rel="noopener noreferrer" aria-label="Google Business"><FaGoogle size={22} color="#fff" /></a>
          <a href="https://www.facebook.com/mercadodistrict/?locale=es_LA" target="_blank" rel="noopener noreferrer" aria-label="Facebook"><FaFacebookF size={22} color="#fff" /></a>
          <a href="https://www.instagram.com/mercadodistrict/?hl=es" target="_blank" rel="noopener noreferrer" aria-label="Instagram"><FaInstagram size={22} color="#fff" /></a>
          <a href="https://www.tiktok.com/@themercadodistrict" target="_blank" rel="noopener noreferrer" aria-label="TikTok"><FaTiktok size={22} color="#fff" /></a>
        </SocialIcons>
      </NavLinks>
    </Nav>
  );
};
