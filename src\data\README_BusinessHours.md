# 🕐 Fake API de Horarios de Negocio

Esta fake API maneja los horarios de todos los establecimientos del food hall de manera dinámica y en tiempo real.

## 📋 Características

- ✅ **Horarios dinámicos** basados en día y hora actual
- ✅ **Estados en tiempo real**: Abierto/Cerrado/Próximamente
- ✅ **Multiidioma** (Español/Inglés)
- ✅ **Horarios específicos** por establecimiento
- ✅ **Horarios por categoría** (Restaurant, Retail, Beauty)
- ✅ **Integración automática** en cards y perfiles

## 🎯 Funciones Principales

### `getStoreStatus(storeName, category, language)`
Obtiene el estado actual de un establecimiento:
```javascript
const status = getStoreStatus('Blu Brasserie', 'Restaurant', 'es');
// Retorna: { status: 'Próximamente', isOpen: false, color: '#F16925', backgroundColor: 'rgba(241, 105, 37, 0.1)' }
```

### `getFormattedHours(storeName, category, language)`
Obtiene horarios formateados para mostrar en UI:
```javascript
const hours = getFormattedHours('Doña Lula', 'Restaurant', 'es');
// Retorna array con: [{ day: 'Lunes', hours: 'Cerrado', isOpen: false }, ...]
```

### `isStoreOpenNow(storeName, category)`
Verifica si está abierto en este momento:
```javascript
const isOpen = isStoreOpenNow('Inka Wasi', 'Restaurant');
// Retorna: true/false
```

## 🏪 Establecimientos Configurados

### Con Horarios Específicos:
- **Blu Brasserie**: Solo Martes-Sábado (Próximamente los demás días)
- **Doña Lula**: Martes-Domingo con horarios especiales
- **Inka Wasi**: Martes-Domingo con horarios especiales

### Por Categoría:
- **Restaurant**: Lunes cerrado, Martes-Jueves 11:00 AM - 10:00 PM, Viernes-Sábado 11:00 AM - 11:00 PM, Domingo 11:00 AM - 6:00 PM
- **Retail**: Lunes cerrado, Martes-Viernes 12:00 PM - 8:00 PM, Sábado 11:00 AM - 8:00 PM, Domingo 11:00 AM - 6:00 PM
- **Beauty**: Lunes cerrado, horarios variados según día

## 🔧 Cómo Agregar Nuevos Establecimientos

1. **Editar `businessHours.js`**:
```javascript
// En specificStores:
"Nuevo Restaurante": {
  monday: { isOpen: false },
  tuesday: { isOpen: true, open: "12:00", close: "21:00" },
  // ... resto de días
}
```

2. **Formato de horas**: Usar formato 24h ("HH:MM")
   - "09:00" = 9:00 AM
   - "15:30" = 3:30 PM
   - "22:00" = 10:00 PM

## 🧪 Testing y Debug

### Usar las utilidades de debug:
```javascript
import { debugStoreHours } from '../utils/businessHoursUtils';
debugStoreHours('Blu Brasserie', 'Restaurant');
```

### Testear en diferentes horarios:
```javascript
import { testStoreAtDifferentTimes } from '../utils/businessHoursUtils';
testStoreAtDifferentTimes('Doña Lula', 'Restaurant');
```

### Ver estado de todos los establecimientos:
```javascript
import { getAllStoresStatus } from '../utils/businessHoursUtils';
getAllStoresStatus();
```

## 🎨 Integración Automática

### En Cards de Stands Virtuales:
- ✅ Colores dinámicos (Verde=Abierto, Rojo=Cerrado, Naranja=Próximamente)
- ✅ Estados en tiempo real
- ✅ Responsive design

### En Perfiles Individuales:
- ✅ Horarios completos de la semana
- ✅ Estado actual destacado
- ✅ Colores por día (Verde=Abierto, Rojo=Cerrado)

## 📱 Estados Posibles

| Estado | Español | English | Color | Cuando |
|--------|---------|---------|-------|--------|
| Abierto | "Abierto Ahora" | "Open Now" | Verde (#28a745) | Dentro del horario |
| Cerrado | "Cerrado" | "Closed" | Rojo (#dc3545) | Fuera del horario |
| Próximamente | "Próximamente" | "Coming Soon" | Naranja (#F16925) | Blu Brasserie cuando está cerrado |

## 🚀 Próximas Mejoras

- [ ] Horarios especiales (feriados)
- [ ] Notificaciones de cambios de horario
- [ ] Integración con base de datos real
- [ ] API de administración para cambiar horarios
- [ ] Zona horaria automática 