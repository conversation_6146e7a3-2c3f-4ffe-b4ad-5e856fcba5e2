import { create } from 'zustand';
import { cache } from 'react';
import { supabase } from '../utils/supabaseClient';

// Request deduplication
let ongoingRequest = null;
let requestPromise = null;

// Single cached function that fetches everything in one optimized query
const fetchTop10StandsData = cache(async () => {
  // Single optimized query using joins
  const { data, error } = await supabase
    .from('top_10')
    .select(`
      number,
      id_local,
      stands!inner(
        id,
        name,
        banner_url,
        avatar,
        slug,
        category,
        description,
        unit,
        unit_type,
        email,
        whatsapp,
        facebook,
        instagram
      )
    `)
    .order('number', { ascending: true })
    .limit(10);
  
  if (error) throw error;
  
  // Transform the joined data
  const transformedData = data?.map(item => ({
    ...item.stands,
    ranking: item.number
  })) || [];
  
  return transformedData;
});

export const useTop10Store = create((set, get) => ({
  top10Stands: [],
  loading: false,
  error: null,
  lastFetch: null,

  fetchTop10Stands: async () => {
    // Request deduplication - prevent multiple simultaneous calls
    if (ongoingRequest) {
      return ongoingRequest;
    }
    
    set({ loading: true, error: null });
    
    // Create promise for deduplication
    requestPromise = (async () => {
      try {
        const data = await fetchTop10StandsData();
        
        set({ 
          top10Stands: data, 
          loading: false, 
          lastFetch: new Date().toISOString()
        });
        
        return data;
        
      } catch (error) {
        set({ 
          top10Stands: [], 
          error: error.message, 
          loading: false 
        });
        throw error;
      } finally {
        // Clear ongoing request
        ongoingRequest = null;
        requestPromise = null;
      }
    })();
    
    ongoingRequest = requestPromise;
    return requestPromise;
  },

  // Force refresh (bypasses cache)
  refreshTop10Stands: async () => {
    // Clear any ongoing request
    ongoingRequest = null;
    requestPromise = null;
    
    return get().fetchTop10Stands();
  },

  // Get cache status and performance metrics
  getCacheStatus: () => {
    const { lastFetch } = get();
    return {
      hasReactCache: true,
      cacheType: 'React built-in cache + Request deduplication',
      invalidation: 'Per server request',
      lastFetch,
      requestDeduplication: 'Active',
      note: 'Single optimized query'
    };
  },

  // Clear cache and force fresh data
  clearCache: () => {
    ongoingRequest = null;
    requestPromise = null;
    set({ lastFetch: null });
  },
})); 